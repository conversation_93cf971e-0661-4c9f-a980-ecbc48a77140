#!/usr/bin/env python3
"""
调试Dify插件搜索问题的脚本
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

async def debug_search_issue():
    """调试搜索问题"""
    
    # 1. 首先创建一个测试记忆给alice用户
    print("📝 创建测试数据...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 创建测试记忆
        test_memory = {
            "messages": [{"role": "user", "content": "我的名字是Alice，我喜欢编程"}],
            "user_id": "alice",
            "metadata": {"name": "Alice", "hobby": "programming"}
        }
        
        response = await client.post(f"{BASE_URL}/v1/memories/", json=test_memory)
        if response.status_code == 200:
            result = response.json()
            memory_id = result[0]["id"] if result else None
            print(f"✅ 创建测试记忆成功: {memory_id}")
        else:
            print(f"❌ 创建测试记忆失败: {response.status_code} - {response.text}")
            return
        
        # 等待索引完成
        await asyncio.sleep(2)
        
        # 2. 测试Dify插件使用的相同参数
        print("\n🔍 测试Dify插件的搜索参数...")
        
        dify_payload = {
            "query": "叫什么名字？",
            "user_id": "alice"
        }
        
        print(f"📤 发送请求: {json.dumps(dify_payload, indent=2, ensure_ascii=False)}")
        
        response = await client.post(
            f"{BASE_URL}/v2/memories/search/",
            json=dify_payload,
            headers={"Authorization": "Token dummy-key"}  # 本地测试不需要真实key
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📄 完整响应结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析响应结构
            print(f"\n🔍 响应分析:")
            print(f"- 响应类型: {type(data)}")
            print(f"- 顶级字段: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
            
            # 检查是否有memories字段（Dify代码期望的）
            if "memories" in data:
                memories = data["memories"]
                print(f"- memories字段存在: {len(memories)} 个记忆")
            else:
                print("- ❌ 没有memories字段（这是问题所在！）")
            
            # 检查实际的results结构
            if "results" in data:
                results = data["results"]
                print(f"- results字段存在: {type(results)}")
                if isinstance(results, dict) and "results" in results:
                    actual_memories = results["results"]
                    print(f"- 实际记忆数据在 results.results 中: {len(actual_memories)} 个记忆")
                    
                    if actual_memories:
                        print(f"- 第一个记忆示例:")
                        print(f"  ID: {actual_memories[0].get('id', 'N/A')}")
                        print(f"  内容: {actual_memories[0].get('memory', 'N/A')}")
                        print(f"  分数: {actual_memories[0].get('score', 'N/A')}")
        else:
            print(f"❌ 搜索失败: {response.text}")
        
        # 3. 测试修复后的格式处理
        print(f"\n🔧 测试修复方案...")
        
        if response.status_code == 200:
            data = response.json()
            
            # 修复后的处理逻辑
            memories = []
            if isinstance(data, dict):
                # 检查V2 API的实际响应格式
                if "results" in data and isinstance(data["results"], dict):
                    if "results" in data["results"]:
                        memories = data["results"]["results"]
                        print(f"✅ 使用修复后的逻辑找到 {len(memories)} 个记忆")
                        
                        for i, memory in enumerate(memories[:2]):
                            print(f"  记忆{i+1}: {memory.get('memory', 'N/A')} (分数: {memory.get('score', 'N/A')})")
                elif "memories" in data:
                    # 兼容可能的其他格式
                    memories = data["memories"]
                    print(f"✅ 使用原始逻辑找到 {len(memories)} 个记忆")
        
        # 4. 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if memory_id:
            response = await client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            if response.status_code == 200:
                print("✅ 测试数据清理完成")

async def main():
    print("🚀 开始调试Dify搜索问题")
    print("=" * 50)
    
    try:
        await debug_search_issue()
        print("\n✅ 调试完成")
    except Exception as e:
        print(f"\n💥 调试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
