#!/usr/bin/env python3
"""
基于Dify官方文档标准诊断插件问题
"""

import asyncio
import json
import httpx
import os

BASE_URL = "http://localhost:8000"

async def diagnose_dify_plugin_standards():
    """基于Dify官方文档标准诊断插件问题"""
    
    print("🔍 基于Dify官方文档标准诊断插件问题")
    print("=" * 60)
    
    # 1. 检查文件结构是否符合标准
    print("\n📁 1. 检查文件结构标准...")
    
    plugin_dir = "mem0-dify-integrated"
    expected_structure = {
        "_assets": "图标和视觉资源",
        "provider": "提供商定义和验证",
        "tools": "工具实现",
        "utils": "辅助函数（可选）",
        "manifest.yaml": "主插件配置",
        "requirements.txt": "依赖列表"
    }
    
    for item, description in expected_structure.items():
        path = os.path.join(plugin_dir, item)
        if os.path.exists(path):
            print(f"✅ {item}: 存在 - {description}")
        else:
            print(f"❌ {item}: 缺失 - {description}")
    
    # 2. 检查工具文件是否符合"一个文件一个工具类"原则
    print("\n🔧 2. 检查工具文件结构...")
    
    tools_dir = os.path.join(plugin_dir, "tools")
    if os.path.exists(tools_dir):
        python_files = [f for f in os.listdir(tools_dir) if f.endswith('.py')]
        yaml_files = [f for f in os.listdir(tools_dir) if f.endswith('.yaml')]
        
        print(f"Python工具文件: {len(python_files)} 个")
        print(f"YAML配置文件: {len(yaml_files)} 个")
        
        # 检查文件对应关系
        for py_file in python_files:
            base_name = py_file[:-3]  # 去掉.py
            yaml_file = base_name + ".yaml"
            if yaml_file in yaml_files:
                print(f"✅ {py_file} ↔ {yaml_file}: 配对正确")
            else:
                print(f"❌ {py_file}: 缺少对应的YAML文件")
    
    # 3. 测试实际的搜索功能
    print("\n🔍 3. 测试搜索功能的参数处理...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 测试用例1: 标准参数
        test_cases = [
            {
                "name": "标准参数测试",
                "payload": {
                    "query": "小红",
                    "user_id": "root",
                    "limit": 10
                }
            },
            {
                "name": "带空字符串参数测试",
                "payload": {
                    "query": "小红",
                    "user_id": "root",
                    "score_range": "",
                    "date_range": "",
                    "enable_graph": "False",
                    "graph_entities": "",
                    "relationship_filter": ""
                }
            },
            {
                "name": "最小参数测试",
                "payload": {
                    "query": "小红",
                    "user_id": "root"
                }
            }
        ]
        
        for case in test_cases:
            print(f"\n测试: {case['name']}")
            print(f"参数: {json.dumps(case['payload'], ensure_ascii=False)}")
            
            try:
                response = await client.post(
                    f"{BASE_URL}/v2/memories/search/",
                    json=case['payload']
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查响应结构
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            memories = data["results"]["results"]
                            print(f"✅ 成功: 找到 {len(memories)} 个记忆")
                            
                            if memories:
                                first_memory = memories[0]
                                print(f"  第一个记忆: {first_memory.get('memory', 'N/A')[:30]}...")
                                print(f"  相关度分数: {first_memory.get('score', 'N/A')}")
                        else:
                            print("❌ 响应结构异常: results.results 不存在")
                    else:
                        print("❌ 响应结构异常: results 字段格式错误")
                        
                    # 检查分页信息
                    total_count = data.get("total_count", 0)
                    print(f"  总数量: {total_count}")
                    
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"  错误信息: {response.text}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
    
    # 4. 分析Dify插件的参数处理模式
    print("\n📋 4. 分析参数处理模式...")
    
    print("根据Dify官方文档，推荐的参数处理模式:")
    print("✅ 必需参数: tool_parameters.get('param_name', '')")
    print("✅ 单个可选参数: tool_parameters.get('optional_param')  # 返回None")
    print("✅ 多个可选参数: 使用try-except模式处理KeyError")
    
    print("\n可能的问题分析:")
    print("1. 空字符串参数可能被误处理为有效参数")
    print("2. 过滤器逻辑可能对空字符串处理不当")
    print("3. V2 API响应格式解析可能存在边界情况")
    
    # 5. 提供修复建议
    print("\n🔧 5. 修复建议...")
    
    suggestions = [
        "检查search_memories.py中的参数验证逻辑",
        "确保空字符串参数不会被添加到filters中",
        "验证V2 API响应解析的完整性",
        "添加更详细的日志记录以便调试",
        "按照Dify标准重构参数处理逻辑"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion}")
    
    print("\n📖 参考文档:")
    print("- Dify插件开发最佳实践: https://docs.dify.ai/plugin-dev-en/0211-getting-started-by-prompt")
    print("- 参数处理模式: 使用.get()方法和try-except处理")
    print("- 文件组织原则: 一个文件一个工具类")

async def main():
    try:
        await diagnose_dify_plugin_standards()
        print("\n✅ 诊断完成")
    except Exception as e:
        print(f"\n💥 诊断异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
