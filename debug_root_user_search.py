#!/usr/bin/env python3
"""
调试root用户搜索小红信息的问题
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

async def debug_root_user_search():
    """调试root用户搜索问题"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        print("🔍 调试root用户搜索小红信息的问题")
        print("=" * 50)
        
        # 1. 首先检查root用户是否有任何记忆
        print("\n📋 1. 检查root用户的所有记忆...")
        
        # 使用V1 API列出记忆
        try:
            response = await client.get(
                f"{BASE_URL}/v1/memories/",
                params={"user_id": "root", "limit": 50}
            )
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get("results", [])
                print(f"✅ V1 API - root用户共有 {len(memories)} 个记忆")
                
                # 显示前几个记忆的内容
                for i, memory in enumerate(memories[:5], 1):
                    print(f"  记忆{i}: {memory.get('memory', 'N/A')}")
                    print(f"    ID: {memory.get('id', 'N/A')}")
                    print(f"    用户ID: {memory.get('user_id', 'N/A')}")
                    print(f"    创建时间: {memory.get('created_at', 'N/A')}")
                    if memory.get('metadata'):
                        print(f"    元数据: {memory.get('metadata')}")
                    print()
            else:
                print(f"❌ V1 API失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ V1 API异常: {e}")
        
        # 2. 使用V2 API获取记忆
        print("\n📋 2. 使用V2 API获取root用户记忆...")
        
        try:
            payload = {
                "user_id": "root",
                "limit": 50
            }
            
            response = await client.post(f"{BASE_URL}/v2/memories/", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                print(f"📄 V2 API完整响应结构:")
                print(f"- 响应字段: {list(data.keys())}")
                
                memories = data.get("memories", [])
                print(f"✅ V2 API - root用户共有 {len(memories)} 个记忆")
                
                # 显示记忆内容
                for i, memory in enumerate(memories[:5], 1):
                    print(f"  记忆{i}: {memory.get('memory', 'N/A')}")
                    print(f"    ID: {memory.get('id', 'N/A')}")
                    print(f"    用户ID: {memory.get('user_id', 'N/A')}")
                    if memory.get('metadata'):
                        print(f"    元数据: {memory.get('metadata')}")
                    print()
            else:
                print(f"❌ V2 API失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ V2 API异常: {e}")
        
        # 3. 测试搜索"小红"
        print("\n🔍 3. 测试搜索'小红'...")
        
        search_payload = {
            "query": "小红",
            "user_id": "root",
            "limit": 10
        }
        
        try:
            response = await client.post(f"{BASE_URL}/v2/memories/search/", json=search_payload)
            
            if response.status_code == 200:
                data = response.json()
                print(f"📄 搜索API完整响应:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 分析响应结构
                print(f"\n🔍 响应分析:")
                print(f"- 响应字段: {list(data.keys())}")
                
                if "results" in data:
                    results = data["results"]
                    print(f"- results类型: {type(results)}")
                    if isinstance(results, dict):
                        print(f"- results字段: {list(results.keys())}")
                        if "results" in results:
                            actual_memories = results["results"]
                            print(f"- 实际记忆数量: {len(actual_memories)}")
                            
                            for i, memory in enumerate(actual_memories, 1):
                                print(f"  搜索结果{i}:")
                                print(f"    内容: {memory.get('memory', 'N/A')}")
                                print(f"    分数: {memory.get('score', 'N/A')}")
                                print(f"    用户ID: {memory.get('user_id', 'N/A')}")
                
                # 检查total_count
                total_count = data.get("total_count", 0)
                print(f"- total_count: {total_count}")
                
                if total_count > 0:
                    print("⚠️ 发现问题：total_count > 0 但没有返回记忆数据！")
                
            else:
                print(f"❌ 搜索失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")
        
        # 4. 测试不同的搜索参数
        print("\n🔍 4. 测试不同的搜索参数...")
        
        test_cases = [
            {"query": "小红", "user_id": "root", "limit": 50},
            {"query": "小红", "user_id": "root", "limit": 1},
            {"query": "红", "user_id": "root", "limit": 10},
            {"query": "", "user_id": "root", "limit": 10},  # 空查询
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试用例{i}: {case}")
            try:
                response = await client.post(f"{BASE_URL}/v2/memories/search/", json=case)
                if response.status_code == 200:
                    data = response.json()
                    total_count = data.get("total_count", 0)
                    
                    # 提取实际记忆数量
                    actual_memories = []
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            actual_memories = data["results"]["results"]
                    
                    print(f"  结果: total_count={total_count}, 实际记忆={len(actual_memories)}")
                    
                    if actual_memories:
                        print(f"  第一个结果: {actual_memories[0].get('memory', 'N/A')[:50]}...")
                else:
                    print(f"  失败: {response.status_code}")
            except Exception as e:
                print(f"  异常: {e}")
        
        # 5. 检查是否有权限或过滤问题
        print("\n🔍 5. 检查权限和过滤问题...")
        
        # 尝试搜索其他用户的数据
        try:
            other_users = ["alice", "test_user", "admin"]
            for user in other_users:
                payload = {
                    "query": "小红",
                    "user_id": user,
                    "limit": 10
                }
                
                response = await client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    total_count = data.get("total_count", 0)
                    print(f"  用户'{user}': total_count={total_count}")
                else:
                    print(f"  用户'{user}': 失败 {response.status_code}")
                    
        except Exception as e:
            print(f"❌ 其他用户测试异常: {e}")

async def main():
    try:
        await debug_root_user_search()
        print("\n✅ 调试完成")
    except Exception as e:
        print(f"\n💥 调试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
