#!/usr/bin/env python3
"""
测试Dify插件修复后的搜索功能
"""

import json
import httpx
import asyncio

class MockDifyTool:
    """模拟Dify工具的核心逻辑"""
    
    def __init__(self):
        self.api_url = "http://localhost:8000"
    
    async def search_memories_v2(self, query: str, user_id: str):
        """模拟修复后的搜索逻辑"""
        
        # 准备V2 payload
        payload = {
            "query": query,
            "user_id": user_id
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    f"{self.api_url}/v2/memories/search/",
                    json=payload
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                # 使用修复后的响应处理逻辑
                memories = []
                pagination_info = {}
                
                if isinstance(response_data, dict):
                    # 处理V2 API的实际响应格式
                    if "results" in response_data and isinstance(response_data["results"], dict):
                        if "results" in response_data["results"]:
                            memories = response_data["results"]["results"]
                    
                    # 构建分页信息
                    if "total_count" in response_data:
                        pagination_info["total_count"] = response_data["total_count"]
                    if "limit" in response_data:
                        pagination_info["limit"] = response_data["limit"]
                    
                    # 兼容旧格式
                    elif "memories" in response_data:
                        memories = response_data["memories"]
                        if "pagination" in response_data:
                            pagination_info = response_data["pagination"]
                
                # 处理记忆数据
                processed_memories = []
                for memory in memories:
                    if not isinstance(memory, dict):
                        continue
                    
                    score_value = memory.get("score")
                    if score_value is None:
                        score_value = 0.0
                    
                    memory_item = {
                        "id": memory.get("id", "unknown"),
                        "memory": memory.get("memory", ""),
                        "score": score_value,
                        "categories": memory.get("categories", []),
                        "created_at": memory.get("created_at", ""),
                        "updated_at": memory.get("updated_at", ""),
                        "metadata": memory.get("metadata", {}),
                        "user_id": memory.get("user_id", ""),
                        "agent_id": memory.get("agent_id", ""),
                        "run_id": memory.get("run_id", "")
                    }
                    processed_memories.append(memory_item)
                
                # 返回结果
                result = {
                    "query": query,
                    "api_version": "v2",
                    "memories": processed_memories,
                    "pagination": pagination_info,
                    "total_found": len(processed_memories)
                }
                
                return result
                
            except Exception as e:
                return {
                    "status": "error",
                    "api_version": "v2",
                    "error": str(e)
                }

async def test_dify_fix():
    """测试修复后的功能"""
    
    print("🚀 测试Dify插件修复")
    print("=" * 40)
    
    # 1. 创建测试数据
    print("📝 创建测试数据...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        test_memory = {
            "messages": [{"role": "user", "content": "我的名字是Alice，我是一名产品经理，喜欢旅行"}],
            "user_id": "alice",
            "metadata": {"name": "Alice", "job": "product manager", "hobby": "travel"}
        }
        
        response = await client.post("http://localhost:8000/v1/memories/", json=test_memory)
        if response.status_code == 200:
            result = response.json()
            memory_id = result[0]["id"] if result else None
            print(f"✅ 创建测试记忆成功: {memory_id}")
        else:
            print(f"❌ 创建测试记忆失败: {response.status_code}")
            return False
        
        await asyncio.sleep(2)  # 等待索引
        
        # 2. 测试修复后的搜索功能
        print("\n🔍 测试修复后的搜索功能...")
        
        tool = MockDifyTool()
        
        test_cases = [
            {
                "query": "叫什么名字？",
                "user_id": "alice",
                "expected": "应该找到Alice的名字信息"
            },
            {
                "query": "工作是什么？",
                "user_id": "alice", 
                "expected": "应该找到产品经理的工作信息"
            },
            {
                "query": "有什么爱好？",
                "user_id": "alice",
                "expected": "应该找到旅行爱好信息"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {case['query']}")
            result = await tool.search_memories_v2(case["query"], case["user_id"])
            
            if "error" in result:
                print(f"❌ 搜索失败: {result['error']}")
            else:
                memories = result.get("memories", [])
                total_found = result.get("total_found", 0)
                
                print(f"✅ 搜索成功: 找到 {total_found} 个相关记忆")
                
                if memories:
                    for j, memory in enumerate(memories[:2], 1):  # 显示前2个
                        print(f"  记忆{j}: {memory['memory']}")
                        print(f"    相关度: {memory['score']:.3f}")
                        if memory['categories']:
                            print(f"    分类: {', '.join(memory['categories'])}")
                else:
                    print("  ⚠️ 没有找到相关记忆")
        
        # 3. 测试原始Dify输入格式
        print(f"\n🎯 测试原始Dify输入格式...")
        original_input = {
            "query": "叫什么名字？",
            "user_id": "alice",
            "enable_graph": "False",
            "graph_entities": "",
            "relationship_filter": ""
        }
        
        result = await tool.search_memories_v2(
            original_input["query"], 
            original_input["user_id"]
        )
        
        if "error" in result:
            print(f"❌ 原始格式测试失败: {result['error']}")
        else:
            memories = result.get("memories", [])
            print(f"✅ 原始格式测试成功: 找到 {len(memories)} 个记忆")
            
            # 模拟Dify的输出格式
            dify_output = {
                "text": f"Query: {result['query']} (V2 API)\n\n",
                "files": [],
                "json": [result]
            }
            
            if memories:
                dify_output["text"] += f"Found {len(memories)} memories:\n"
                for idx, memory in enumerate(memories, 1):
                    dify_output["text"] += f"\n{idx}. Memory: {memory['memory']}"
                    dify_output["text"] += f"\n   Score: {memory['score']:.3f}"
                    if memory['categories']:
                        dify_output["text"] += f"\n   Categories: {', '.join(memory['categories'])}"
            else:
                dify_output["text"] += "No memories found."
            
            print(f"📤 Dify输出格式:")
            print(json.dumps(dify_output, indent=2, ensure_ascii=False))
        
        # 4. 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if memory_id:
            response = await client.delete(f"http://localhost:8000/v1/memories/{memory_id}/")
            if response.status_code == 200:
                print("✅ 清理完成")
        
        return True

async def main():
    try:
        success = await test_dify_fix()
        if success:
            print(f"\n🎉 Dify插件修复测试完成！")
        else:
            print(f"\n💥 测试失败")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
