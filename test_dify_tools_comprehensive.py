#!/usr/bin/env python3
"""
全面测试Dify插件工具
"""

import asyncio
import json
import logging
import sys
import httpx

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"
TEST_USER_ID = "dify_comprehensive_test"

class ComprehensiveDifyTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_memory_ids = []
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await self.client.aclose()
    
    async def cleanup(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        for memory_id in self.test_memory_ids:
            try:
                await self.client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            except:
                pass
    
    async def test_add_memory_tool(self):
        """测试添加记忆工具"""
        logger.info("🔍 测试添加记忆工具...")
        
        # 测试用例1: 基本添加
        payload1 = {
            "messages": [
                {"role": "user", "content": "我的名字是张三，我是一名软件工程师"},
                {"role": "assistant", "content": "好的，我记住了您是张三，职业是软件工程师"}
            ],
            "user_id": TEST_USER_ID,
            "metadata": {"name": "张三", "profession": "software engineer"}
        }
        
        try:
            response = await self.client.post(f"{BASE_URL}/v1/memories/", json=payload1)
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0:
                    memory_id = result[0].get("id")
                    if memory_id:
                        self.test_memory_ids.append(memory_id)
                        logger.info(f"✅ 基本添加成功: {memory_id}")
                        return {"success": True, "memory_id": memory_id}
            
            logger.error(f"❌ 基本添加失败: {response.status_code} - {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
        except Exception as e:
            logger.error(f"❌ 添加记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_search_memories_tool(self):
        """测试搜索记忆工具（修复后）"""
        logger.info("🔍 测试搜索记忆工具...")
        
        await asyncio.sleep(2)  # 等待索引
        
        test_cases = [
            {"query": "叫什么名字", "expected": "应该找到张三"},
            {"query": "职业是什么", "expected": "应该找到软件工程师"},
            {"query": "个人信息", "expected": "应该找到相关信息"}
        ]
        
        results = {}
        for case in test_cases:
            payload = {
                "query": case["query"],
                "user_id": TEST_USER_ID,
                "limit": 5
            }
            
            try:
                response = await self.client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    
                    # 使用修复后的逻辑
                    memories = []
                    if isinstance(data, dict):
                        if "results" in data and isinstance(data["results"], dict):
                            if "results" in data["results"]:
                                memories = data["results"]["results"]
                        elif "memories" in data:
                            memories = data["memories"]
                    
                    results[case["query"]] = {
                        "success": True,
                        "count": len(memories),
                        "memories": memories[:2]  # 只保留前2个
                    }
                    logger.info(f"✅ 搜索'{case['query']}': {len(memories)} 个结果")
                else:
                    results[case["query"]] = {
                        "success": False,
                        "error": f"HTTP {response.status_code}"
                    }
                    logger.error(f"❌ 搜索'{case['query']}'失败: {response.status_code}")
                    
            except Exception as e:
                results[case["query"]] = {
                    "success": False,
                    "error": str(e)
                }
                logger.error(f"❌ 搜索'{case['query']}'异常: {e}")
        
        return results
    
    async def test_list_memories_tool(self):
        """测试列出记忆工具"""
        logger.info("🔍 测试列出记忆工具...")
        
        # 测试V1 API
        try:
            response = await self.client.get(
                f"{BASE_URL}/v1/memories/",
                params={"user_id": TEST_USER_ID, "limit": 10}
            )
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get("results", []) if isinstance(data, dict) else []
                logger.info(f"✅ V1列出记忆成功: {len(memories)} 个记忆")
                return {"success": True, "api_version": "v1", "count": len(memories)}
            else:
                logger.error(f"❌ V1列出记忆失败: {response.status_code}")
                return {"success": False, "error": f"V1 HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ 列出记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_v2_get_memories_tool(self):
        """测试V2获取记忆工具"""
        logger.info("🔍 测试V2获取记忆工具...")
        
        payload = {
            "user_id": TEST_USER_ID,
            "limit": 10
        }
        
        try:
            response = await self.client.post(f"{BASE_URL}/v2/memories/", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get("memories", []) if isinstance(data, dict) else []
                logger.info(f"✅ V2获取记忆成功: {len(memories)} 个记忆")
                return {"success": True, "api_version": "v2", "count": len(memories)}
            else:
                logger.error(f"❌ V2获取记忆失败: {response.status_code}")
                return {"success": False, "error": f"V2 HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ V2获取记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_update_memory_tool(self):
        """测试更新记忆工具"""
        logger.info("🔍 测试更新记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可更新的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids[0]
        payload = {
            "text": "我的名字是张三，我是一名高级软件工程师（已更新）"
        }
        
        try:
            response = await self.client.put(f"{BASE_URL}/v1/memories/{memory_id}/", json=payload)
            
            if response.status_code == 200:
                logger.info(f"✅ 更新记忆成功: {memory_id}")
                return {"success": True, "memory_id": memory_id}
            else:
                logger.error(f"❌ 更新记忆失败: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ 更新记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_get_single_memory_tool(self):
        """测试获取单个记忆工具"""
        logger.info("🔍 测试获取单个记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可获取的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids[0]
        
        try:
            response = await self.client.get(f"{BASE_URL}/v1/memories/{memory_id}/")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 获取单个记忆成功: {memory_id}")
                logger.info(f"  内容: {result.get('memory', 'N/A')[:50]}...")
                return {"success": True, "memory_id": memory_id, "content": result.get('memory', '')}
            else:
                logger.error(f"❌ 获取单个记忆失败: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ 获取单个记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_delete_memory_tool(self):
        """测试删除记忆工具"""
        logger.info("🔍 测试删除记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可删除的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids.pop()  # 取出一个进行删除测试
        
        try:
            response = await self.client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            
            if response.status_code == 200:
                logger.info(f"✅ 删除记忆成功: {memory_id}")
                return {"success": True, "memory_id": memory_id}
            else:
                logger.error(f"❌ 删除记忆失败: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ 删除记忆异常: {e}")
            return {"success": False, "error": str(e)}

async def main():
    """主测试函数"""
    logger.info("🚀 开始全面测试Dify插件工具")
    logger.info("=" * 60)
    
    async with ComprehensiveDifyTester() as tester:
        test_results = {}
        
        # 按顺序执行测试
        test_results["add_memory"] = await tester.test_add_memory_tool()
        test_results["search_memories"] = await tester.test_search_memories_tool()
        test_results["list_memories"] = await tester.test_list_memories_tool()
        test_results["v2_get_memories"] = await tester.test_v2_get_memories_tool()
        test_results["get_single_memory"] = await tester.test_get_single_memory_tool()
        test_results["update_memory"] = await tester.test_update_memory_tool()
        test_results["delete_memory"] = await tester.test_delete_memory_tool()
        
        # 输出测试结果摘要
        logger.info("\n" + "=" * 60)
        logger.info("📊 Dify工具全面测试结果摘要")
        logger.info("=" * 60)
        
        passed_tests = 0
        total_tests = 0
        
        for tool_name, result in test_results.items():
            if tool_name == "search_memories" and isinstance(result, dict):
                # 搜索工具有多个子测试
                for query, sub_result in result.items():
                    total_tests += 1
                    if sub_result["success"]:
                        passed_tests += 1
                        logger.info(f"✅ {tool_name}[{query}]: 通过")
                    else:
                        logger.error(f"❌ {tool_name}[{query}]: 失败")
            else:
                total_tests += 1
                if result["success"]:
                    passed_tests += 1
                    logger.info(f"✅ {tool_name}: 通过")
                else:
                    logger.error(f"❌ {tool_name}: 失败 - {result.get('error', '未知错误')}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"\n🎯 总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 70:
            logger.info("🎉 Dify工具测试基本通过！")
            return True
        else:
            logger.error("💥 Dify工具测试存在较多问题")
            return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 测试执行异常: {e}")
        sys.exit(1)
