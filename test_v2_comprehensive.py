#!/usr/bin/env python3
"""
V2 API 全面功能测试脚本

基于官方文档测试以下功能：
1. V2 获取memories - 基本功能和复杂过滤器
2. V2 搜索memories - 基本搜索和高级过滤
3. 比较操作符测试 (in, gte, lte, gt, lt, ne, icontains)
4. 逻辑操作符测试 (AND, OR, NOT)
5. 通配符功能测试 (*)
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
import httpx

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"
TEST_USER_ID = "alex"  # 使用文档中的示例用户
TEST_AGENT_ID = "travel-agent"
TEST_RUN_ID = "session-001"

class V2APIComprehensiveTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_memory_ids = []
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await self.client.aclose()
    
    async def cleanup(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        for memory_id in self.test_memory_ids:
            try:
                await self.client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            except:
                pass
    
    async def setup_test_data(self):
        """设置测试数据"""
        logger.info("📝 设置测试数据...")
        
        # 创建不同类型的测试记忆
        test_memories = [
            {
                "messages": [{"role": "user", "content": "Name: Alex. Vegetarian. Allergic to nuts."}],
                "user_id": TEST_USER_ID,
                "metadata": {"category": "personal", "dietary": "vegetarian", "allergies": "nuts"}
            },
            {
                "messages": [{"role": "user", "content": "Alice likes to play cricket on weekends."}],
                "user_id": "alice",
                "agent_id": "sports-agent",
                "metadata": {"category": "hobbies", "sport": "cricket", "frequency": "weekly"}
            },
            {
                "messages": [{"role": "user", "content": "Planning a trip to Japan next year."}],
                "user_id": TEST_USER_ID,
                "agent_id": TEST_AGENT_ID,
                "run_id": TEST_RUN_ID,
                "metadata": {"category": "travel", "destination": "japan", "year": 2025}
            },
            {
                "messages": [{"role": "user", "content": "Prefers luxury hotels with spa services."}],
                "user_id": TEST_USER_ID,
                "agent_id": TEST_AGENT_ID,
                "metadata": {"category": "travel", "accommodation": "luxury", "amenities": ["spa"]}
            },
            {
                "messages": [{"role": "user", "content": "Budget for travel is $5000."}],
                "user_id": TEST_USER_ID,
                "run_id": TEST_RUN_ID,
                "metadata": {"category": "budget", "amount": 5000, "currency": "USD"}
            }
        ]
        
        for i, memory_data in enumerate(test_memories):
            try:
                response = await self.client.post(f"{BASE_URL}/v1/memories/", json=memory_data)
                if response.status_code == 200:
                    result = response.json()
                    if result and len(result) > 0:
                        memory_id = result[0].get("id")
                        if memory_id:
                            self.test_memory_ids.append(memory_id)
                            logger.info(f"✅ 创建测试记忆 {i+1}: {memory_id}")
            except Exception as e:
                logger.error(f"❌ 创建测试记忆 {i+1} 失败: {e}")
        
        # 等待索引完成
        await asyncio.sleep(3)
        logger.info(f"📊 成功创建 {len(self.test_memory_ids)} 个测试记忆")
    
    async def test_v2_get_basic(self):
        """测试V2获取API基本功能"""
        logger.info("🔍 测试V2获取API基本功能...")
        
        # 测试按user_id获取（文档示例）
        payload = {
            "user_id": TEST_USER_ID,
            "limit": 10
        }
        
        response = await self.client.post(f"{BASE_URL}/v2/memories/", json=payload)
        if response.status_code == 200:
            data = response.json()
            memories = data.get("memories", [])
            logger.info(f"✅ 按user_id获取成功: {len(memories)} 个记忆")
            return True
        else:
            logger.error(f"❌ 按user_id获取失败: {response.status_code} - {response.text}")
            return False
    
    async def test_v2_get_complex_filters(self):
        """测试V2获取API复杂过滤器"""
        logger.info("🔍 测试V2获取API复杂过滤器...")
        
        test_cases = [
            {
                "name": "AND逻辑过滤器",
                "payload": {
                    "user_id": TEST_USER_ID,
                    "filters": {
                        "AND": [
                            {"metadata.category": "travel"}
                        ]
                    }
                }
            },
            {
                "name": "OR逻辑过滤器", 
                "payload": {
                    "filters": {
                        "OR": [
                            {"user_id": TEST_USER_ID},
                            {"user_id": "alice"}
                        ]
                    }
                }
            },
            {
                "name": "通配符过滤器",
                "payload": {
                    "user_id": TEST_USER_ID,
                    "filters": {
                        "AND": [
                            {"run_id": "*"}
                        ]
                    }
                }
            },
            {
                "name": "比较操作符 - in",
                "payload": {
                    "filters": {
                        "AND": [
                            {"agent_id": {"in": ["travel-agent", "sports-agent"]}}
                        ]
                    }
                }
            }
        ]
        
        results = {}
        for case in test_cases:
            try:
                response = await self.client.post(f"{BASE_URL}/v2/memories/", json=case["payload"])
                if response.status_code == 200:
                    data = response.json()
                    memories = data.get("memories", [])
                    results[case["name"]] = len(memories)
                    logger.info(f"✅ {case['name']}: {len(memories)} 个记忆")
                else:
                    logger.error(f"❌ {case['name']}: {response.status_code} - {response.text}")
                    results[case["name"]] = 0
            except Exception as e:
                logger.error(f"❌ {case['name']} 异常: {e}")
                results[case["name"]] = 0
        
        return results
    
    async def test_v2_search_basic(self):
        """测试V2搜索API基本功能"""
        logger.info("🔍 测试V2搜索API基本功能...")
        
        # 基于文档示例的搜索
        payload = {
            "query": "What are Alice's hobbies?",
            "filters": {
                "OR": [
                    {"user_id": "alice"},
                    {"agent_id": {"in": ["travel-agent", "sports-agent"]}}
                ]
            }
        }
        
        response = await self.client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
        if response.status_code == 200:
            data = response.json()
            # 处理实际的响应格式
            if "results" in data and isinstance(data["results"], dict):
                memories = data["results"].get("results", [])
                logger.info(f"✅ V2搜索基本功能成功: {len(memories)} 个相关记忆")
                
                # 显示搜索结果详情
                for i, memory in enumerate(memories[:2]):
                    score = memory.get("score", 0)
                    content = memory.get("memory", "N/A")
                    logger.info(f"  结果{i+1}: {content} (相关度: {score:.3f})")
                
                return True
            else:
                logger.error(f"❌ 响应格式异常: {data}")
                return False
        else:
            logger.error(f"❌ V2搜索失败: {response.status_code} - {response.text}")
            return False
    
    async def test_v2_search_advanced(self):
        """测试V2搜索API高级功能"""
        logger.info("🔍 测试V2搜索API高级功能...")
        
        test_cases = [
            {
                "name": "通配符搜索",
                "payload": {
                    "query": "travel plans",
                    "user_id": TEST_USER_ID,
                    "filters": {
                        "AND": [
                            {"run_id": "*"}
                        ]
                    }
                }
            },
            {
                "name": "元数据过滤搜索",
                "payload": {
                    "query": "dietary preferences",
                    "user_id": TEST_USER_ID,
                    "filters": {
                        "AND": [
                            {"metadata.category": "personal"}
                        ]
                    }
                }
            },
            {
                "name": "多条件OR搜索",
                "payload": {
                    "query": "hobbies and travel",
                    "filters": {
                        "OR": [
                            {"metadata.category": "hobbies"},
                            {"metadata.category": "travel"}
                        ]
                    }
                }
            }
        ]
        
        results = {}
        for case in test_cases:
            try:
                response = await self.client.post(f"{BASE_URL}/v2/memories/search/", json=case["payload"])
                if response.status_code == 200:
                    data = response.json()
                    if "results" in data and isinstance(data["results"], dict):
                        memories = data["results"].get("results", [])
                        results[case["name"]] = len(memories)
                        logger.info(f"✅ {case['name']}: {len(memories)} 个相关记忆")
                    else:
                        results[case["name"]] = 0
                        logger.error(f"❌ {case['name']}: 响应格式异常")
                else:
                    logger.error(f"❌ {case['name']}: {response.status_code} - {response.text}")
                    results[case["name"]] = 0
            except Exception as e:
                logger.error(f"❌ {case['name']} 异常: {e}")
                results[case["name"]] = 0
        
        return results

async def main():
    """主测试函数"""
    logger.info("🚀 开始V2 API全面功能测试")
    logger.info("=" * 60)
    
    async with V2APIComprehensiveTester() as tester:
        # 设置测试数据
        await tester.setup_test_data()
        
        # 执行测试
        test_results = {}
        
        # 基本功能测试
        test_results["basic_get"] = await tester.test_v2_get_basic()
        test_results["complex_filters"] = await tester.test_v2_get_complex_filters()
        test_results["basic_search"] = await tester.test_v2_search_basic()
        test_results["advanced_search"] = await tester.test_v2_search_advanced()
        
        # 输出测试结果
        logger.info("\n" + "=" * 60)
        logger.info("📊 V2 API测试结果摘要")
        logger.info("=" * 60)
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        logger.info(f"✅ 基本获取功能: {'通过' if test_results['basic_get'] else '失败'}")
        logger.info(f"✅ 基本搜索功能: {'通过' if test_results['basic_search'] else '失败'}")
        
        if isinstance(test_results['complex_filters'], dict):
            logger.info("📋 复杂过滤器测试结果:")
            for filter_name, count in test_results['complex_filters'].items():
                logger.info(f"  - {filter_name}: {count} 个记忆")
        
        if isinstance(test_results['advanced_search'], dict):
            logger.info("📋 高级搜索测试结果:")
            for search_name, count in test_results['advanced_search'].items():
                logger.info(f"  - {search_name}: {count} 个记忆")
        
        logger.info(f"\n🎯 总体评估: V2 API核心功能{'正常' if test_results['basic_get'] and test_results['basic_search'] else '存在问题'}")
        
        return test_results['basic_get'] and test_results['basic_search']

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 测试执行异常: {e}")
        sys.exit(1)
