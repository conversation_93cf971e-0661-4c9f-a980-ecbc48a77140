#!/usr/bin/env python3
"""
测试Dify搜索工具修复后的效果
"""

import asyncio
import json
import sys
import os
sys.path.append('/opt/mem0ai/mem0-dify-integrated')

# 模拟Dify工具的运行环境
class MockRuntime:
    def __init__(self):
        self.credentials = {
            "mem0_api_key": "dummy-key",
            "mem0_api_url": "http://localhost:8000"
        }

# 导入修复后的搜索工具
from tools.search_memories import SearchMemoriesTool

async def test_fixed_search_tool():
    """测试修复后的搜索工具"""
    
    print("🔍 测试修复后的Dify搜索工具")
    print("=" * 50)
    
    # 创建工具实例
    tool = SearchMemoriesTool()
    tool.runtime = MockRuntime()
    
    # 测试用例：原始问题的输入参数
    original_problem_input = {
        "query": "小红",
        "user_id": "root",
        "score_range": "",
        "date_range": "",
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    
    print("📋 测试原始问题的输入参数:")
    print(json.dumps(original_problem_input, indent=2, ensure_ascii=False))
    
    try:
        # 执行搜索
        print("\n🚀 执行搜索...")
        results = []
        
        # 收集所有yield的消息
        async for message in tool._invoke(original_problem_input):
            results.append(message)
        
        print(f"✅ 搜索完成，收到 {len(results)} 个消息")
        
        # 分析结果
        text_messages = []
        json_messages = []
        
        for result in results:
            if hasattr(result, 'type'):
                if result.type == 'text':
                    text_messages.append(result.message)
                elif result.type == 'json':
                    json_messages.append(result.message)
        
        print(f"\n📄 文本消息 ({len(text_messages)} 个):")
        for i, text in enumerate(text_messages, 1):
            print(f"{i}. {text[:100]}{'...' if len(text) > 100 else ''}")
        
        print(f"\n📋 JSON消息 ({len(json_messages)} 个):")
        for i, json_msg in enumerate(json_messages, 1):
            if isinstance(json_msg, dict):
                memories_count = len(json_msg.get('memories', []))
                total_found = json_msg.get('total_found', 0)
                print(f"{i}. 找到记忆: {memories_count} 个, 总计: {total_found}")
                
                # 显示前几个记忆
                memories = json_msg.get('memories', [])
                for j, memory in enumerate(memories[:3], 1):
                    print(f"   记忆{j}: {memory.get('memory', 'N/A')[:50]}...")
                    print(f"          分数: {memory.get('score', 'N/A')}")
            else:
                print(f"{i}. {json_msg}")
        
        # 检查是否解决了原始问题
        if json_messages:
            json_result = json_messages[0]
            if isinstance(json_result, dict):
                memories = json_result.get('memories', [])
                if memories:
                    print(f"\n🎉 问题已解决！成功找到 {len(memories)} 个关于'小红'的记忆")
                    return True
                else:
                    print(f"\n❌ 问题仍然存在：没有找到记忆")
                    return False
        
        print(f"\n⚠️ 无法确定结果状态")
        return False
        
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_parameter_handling():
    """测试参数处理逻辑"""
    
    print("\n🔧 测试参数处理逻辑")
    print("=" * 30)
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "空字符串参数",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "",
                "enable_graph": "False"
            }
        },
        {
            "name": "None参数",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": None,
                "enable_graph": None
            }
        },
        {
            "name": "有效参数",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "0.5-1.0",
                "enable_graph": "True"
            }
        },
        {
            "name": "最小参数",
            "params": {
                "query": "小红",
                "user_id": "root"
            }
        }
    ]
    
    tool = SearchMemoriesTool()
    tool.runtime = MockRuntime()
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        try:
            results = []
            async for message in tool._invoke(case['params']):
                results.append(message)
            
            # 检查JSON结果
            json_results = [r.message for r in results if hasattr(r, 'type') and r.type == 'json']
            if json_results:
                memories_count = len(json_results[0].get('memories', []))
                print(f"✅ 成功: 找到 {memories_count} 个记忆")
            else:
                print(f"❌ 失败: 没有JSON结果")
                
        except Exception as e:
            print(f"❌ 异常: {e}")

async def main():
    """主测试函数"""
    
    print("🚀 开始测试Dify搜索工具修复效果")
    print("=" * 60)
    
    # 测试修复后的搜索功能
    success = await test_fixed_search_tool()
    
    # 测试参数处理
    await test_parameter_handling()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 修复验证成功！Dify搜索工具现在可以正常工作了")
    else:
        print("💥 修复验证失败，需要进一步调试")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"💥 测试执行异常: {e}")
        sys.exit(1)
