#!/usr/bin/env python3
"""
测试参数处理修复的效果
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

def simulate_dify_parameter_processing(tool_parameters):
    """模拟修复后的Dify参数处理逻辑"""
    
    # 基础payload
    payload = {
        "query": tool_parameters.get("query", ""),
    }
    
    # 添加身份标识符
    if tool_parameters.get("user_id"):
        payload["user_id"] = tool_parameters["user_id"]
    if tool_parameters.get("agent_id"):
        payload["agent_id"] = tool_parameters["agent_id"]
    if tool_parameters.get("run_id"):
        payload["run_id"] = tool_parameters["run_id"]
    
    # 初始化filters字典
    filters_dict = {}
    
    # 处理分页
    limit = tool_parameters.get("limit", 10)
    if limit and 1 <= limit <= 100:
        payload["limit"] = limit
    
    # 修复后的过滤器参数处理
    score_range = tool_parameters.get("score_range")
    if score_range and score_range.strip():
        filters_dict["score_range"] = score_range

    date_range = tool_parameters.get("date_range")
    if date_range and date_range.strip():
        filters_dict["date_range"] = date_range

    category_filter = tool_parameters.get("category_filter")
    if category_filter and category_filter.strip():
        filters_dict["categories"] = category_filter
    
    # 将filters_dict添加到payload中（如果不为空）
    if filters_dict:
        payload["filters"] = filters_dict
    
    # 修复后的图谱参数处理
    enable_graph = tool_parameters.get("enable_graph")
    if enable_graph and enable_graph.strip() and enable_graph.lower() != "false":
        payload["enable_graph"] = enable_graph

    graph_entities = tool_parameters.get("graph_entities")
    if graph_entities and graph_entities.strip():
        payload["graph_entities"] = graph_entities

    relationship_filter = tool_parameters.get("relationship_filter")
    if relationship_filter and relationship_filter.strip():
        payload["relationship_filter"] = relationship_filter
    
    return payload

async def test_parameter_fix():
    """测试参数处理修复"""
    
    print("🔍 测试参数处理修复效果")
    print("=" * 50)
    
    # 原始问题的输入参数
    original_input = {
        "query": "小红",
        "user_id": "root",
        "score_range": "",
        "date_range": "",
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    
    print("📋 原始输入参数:")
    print(json.dumps(original_input, indent=2, ensure_ascii=False))
    
    # 修复前的处理（模拟）
    print("\n🔧 修复前的payload（模拟）:")
    old_payload = {
        "query": "小红",
        "user_id": "root",
        "limit": 10,
        "filters": {
            "score_range": "",
            "date_range": ""
        },
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    print(json.dumps(old_payload, indent=2, ensure_ascii=False))
    
    # 修复后的处理
    print("\n✅ 修复后的payload:")
    new_payload = simulate_dify_parameter_processing(original_input)
    print(json.dumps(new_payload, indent=2, ensure_ascii=False))
    
    # 测试实际API调用
    print("\n🚀 测试实际API调用...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/v2/memories/search/",
                json=new_payload
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 提取记忆数据
                memories = []
                if "results" in data and isinstance(data["results"], dict):
                    if "results" in data["results"]:
                        memories = data["results"]["results"]
                
                print(f"✅ API调用成功: 找到 {len(memories)} 个记忆")
                
                if memories:
                    print("📋 记忆详情:")
                    for i, memory in enumerate(memories[:3], 1):
                        print(f"  {i}. {memory.get('memory', 'N/A')}")
                        print(f"     分数: {memory.get('score', 'N/A'):.3f}")
                        print(f"     用户: {memory.get('user_id', 'N/A')}")
                    
                    # 模拟Dify输出格式
                    dify_output = {
                        "text": f"Query: 小红 (V2 API)\n\nFound {len(memories)} memories:\n\n",
                        "files": [],
                        "json": [{
                            "api_version": "v2",
                            "memories": memories,
                            "pagination": {
                                "total_count": data.get("total_count", len(memories)),
                                "limit": new_payload.get("limit", 10)
                            },
                            "query": "小红",
                            "total_found": len(memories)
                        }]
                    }
                    
                    # 添加记忆详情到文本
                    for i, memory in enumerate(memories, 1):
                        dify_output["text"] += f"{i}. Memory: {memory['memory']}\n"
                        dify_output["text"] += f"   Score: {memory['score']:.3f}\n"
                        if memory.get('categories'):
                            dify_output["text"] += f"   Categories: {', '.join(memory['categories'])}\n"
                        dify_output["text"] += f"   Created: {memory['created_at']}\n\n"
                    
                    dify_output["text"] += f"Pagination: {dify_output['json'][0]['pagination']}"
                    
                    print(f"\n📤 修复后的Dify输出格式:")
                    print(json.dumps(dify_output, indent=2, ensure_ascii=False))
                    
                    return True
                else:
                    print("❌ 没有找到记忆")
                    return False
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return False

async def test_different_scenarios():
    """测试不同场景"""
    
    print("\n🔍 测试不同参数场景")
    print("=" * 30)
    
    test_cases = [
        {
            "name": "空字符串参数（原始问题）",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "",
                "enable_graph": "False"
            }
        },
        {
            "name": "有效过滤参数",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "0.5-1.0",
                "enable_graph": "True"
            }
        },
        {
            "name": "最小参数",
            "params": {
                "query": "小红",
                "user_id": "root"
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for case in test_cases:
            print(f"\n测试: {case['name']}")
            payload = simulate_dify_parameter_processing(case['params'])
            print(f"Payload: {json.dumps(payload, ensure_ascii=False)}")
            
            try:
                response = await client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    memories = []
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            memories = data["results"]["results"]
                    print(f"✅ 成功: 找到 {len(memories)} 个记忆")
                else:
                    print(f"❌ 失败: {response.status_code}")
            except Exception as e:
                print(f"❌ 异常: {e}")

async def main():
    """主测试函数"""
    
    print("🚀 开始测试参数处理修复")
    print("=" * 60)
    
    success = await test_parameter_fix()
    await test_different_scenarios()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 参数处理修复验证成功！")
        print("✅ 问题已解决：空字符串参数不再干扰搜索结果")
        print("✅ Dify插件现在应该可以正常返回搜索结果")
    else:
        print("💥 修复验证失败")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"💥 测试执行异常: {e}")
        sys.exit(1)
