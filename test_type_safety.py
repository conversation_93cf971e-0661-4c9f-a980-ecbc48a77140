#!/usr/bin/env python3
"""
测试类型安全修复
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

def simulate_dify_parameter_processing_fixed(tool_parameters):
    """模拟修复后的参数处理逻辑（包含类型安全）"""
    
    payload = {
        "query": tool_parameters.get("query", ""),
    }
    
    # 添加身份标识符
    if tool_parameters.get("user_id"):
        payload["user_id"] = tool_parameters["user_id"]
    if tool_parameters.get("agent_id"):
        payload["agent_id"] = tool_parameters["agent_id"]
    if tool_parameters.get("run_id"):
        payload["run_id"] = tool_parameters["run_id"]
    
    # 初始化filters字典
    filters_dict = {}
    
    # 处理分页
    limit = tool_parameters.get("limit", 10)
    if limit and 1 <= limit <= 100:
        payload["limit"] = limit
    
    # 修复后的过滤器参数处理（类型安全）
    score_range = tool_parameters.get("score_range")
    if score_range and isinstance(score_range, str) and score_range.strip():
        filters_dict["score_range"] = score_range

    date_range = tool_parameters.get("date_range")
    if date_range and isinstance(date_range, str) and date_range.strip():
        filters_dict["date_range"] = date_range

    category_filter = tool_parameters.get("category_filter")
    if category_filter and isinstance(category_filter, str) and category_filter.strip():
        filters_dict["categories"] = category_filter
    
    # 将filters_dict添加到payload中（如果不为空）
    if filters_dict:
        payload["filters"] = filters_dict
    
    # 修复后的图谱参数处理（类型安全）
    enable_graph = tool_parameters.get("enable_graph")
    if enable_graph is not None:
        # 处理布尔值类型
        if isinstance(enable_graph, bool):
            if enable_graph:  # True
                payload["enable_graph"] = enable_graph
        # 处理字符串类型
        elif isinstance(enable_graph, str):
            if enable_graph.strip() and enable_graph.lower() not in ["false", "0", "no", "off"]:
                payload["enable_graph"] = enable_graph

    graph_entities = tool_parameters.get("graph_entities")
    if graph_entities and isinstance(graph_entities, str) and graph_entities.strip():
        payload["graph_entities"] = graph_entities

    relationship_filter = tool_parameters.get("relationship_filter")
    if relationship_filter and isinstance(relationship_filter, str) and relationship_filter.strip():
        payload["relationship_filter"] = relationship_filter
    
    return payload

async def test_type_safety():
    """测试类型安全修复"""
    
    print("🔍 测试类型安全修复")
    print("=" * 40)
    
    # 测试用例：包含各种类型的参数
    test_cases = [
        {
            "name": "布尔值True",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": True  # 布尔值True
            },
            "should_include_graph": True
        },
        {
            "name": "布尔值False",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": False  # 布尔值False
            },
            "should_include_graph": False
        },
        {
            "name": "字符串True",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": "True"  # 字符串"True"
            },
            "should_include_graph": True
        },
        {
            "name": "字符串False",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": "False"  # 字符串"False"
            },
            "should_include_graph": False
        },
        {
            "name": "字符串false（小写）",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": "false"  # 字符串"false"
            },
            "should_include_graph": False
        },
        {
            "name": "空字符串",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": ""  # 空字符串
            },
            "should_include_graph": False
        },
        {
            "name": "原始问题场景",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "",
                "date_range": "",
                "enable_graph": "False",
                "graph_entities": "",
                "relationship_filter": ""
            },
            "should_include_graph": False
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        for case in test_cases:
            print(f"\n📋 测试用例: {case['name']}")
            print(f"输入参数: {json.dumps(case['params'], ensure_ascii=False)}")
            
            # 处理参数
            payload = simulate_dify_parameter_processing_fixed(case['params'])
            print(f"处理后payload: {json.dumps(payload, ensure_ascii=False)}")
            
            # 验证enable_graph处理是否正确
            has_enable_graph = "enable_graph" in payload
            expected = case['should_include_graph']
            
            if has_enable_graph == expected:
                print(f"✅ enable_graph处理正确: {'包含' if has_enable_graph else '不包含'}")
            else:
                print(f"❌ enable_graph处理错误: 期望{'包含' if expected else '不包含'}，实际{'包含' if has_enable_graph else '不包含'}")
            
            # 测试实际API调用
            try:
                response = await client.post(
                    f"{BASE_URL}/v2/memories/search/",
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 提取记忆数据
                    memories = []
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            memories = data["results"]["results"]
                    
                    print(f"✅ API调用成功: 找到 {len(memories)} 个记忆")
                    
                    if len(memories) > 0:
                        print(f"  第一个记忆: {memories[0].get('memory', 'N/A')}")
                        print(f"  相关度分数: {memories[0].get('score', 'N/A'):.3f}")
                    
                else:
                    print(f"❌ API调用失败: {response.status_code}")
                    print(f"  错误信息: {response.text}")
                    
            except Exception as e:
                print(f"❌ API调用异常: {e}")

async def test_original_problem():
    """测试原始问题是否解决"""
    
    print(f"\n🎯 测试原始问题是否解决")
    print("=" * 40)
    
    # 用户报告的原始问题参数
    original_params = {
        "query": "小红",
        "user_id": "root",
        "enable_graph": "False"
    }
    
    print(f"📋 原始问题参数:")
    print(json.dumps(original_params, indent=2, ensure_ascii=False))
    
    # 处理参数
    payload = simulate_dify_parameter_processing_fixed(original_params)
    print(f"\n📤 修复后的payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    # 测试API调用
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/v2/memories/search/",
                json=payload
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 模拟Dify的处理逻辑
                memories = []
                pagination_info = {}
                
                if isinstance(response_data, dict):
                    if "results" in response_data and isinstance(response_data["results"], dict):
                        if "results" in response_data["results"]:
                            memories = response_data["results"]["results"]
                    
                    if "total_count" in response_data:
                        pagination_info["total_count"] = response_data["total_count"]
                    if "limit" in response_data:
                        pagination_info["limit"] = response_data["limit"]
                
                # 处理记忆数据
                processed_memories = []
                for memory in memories:
                    if isinstance(memory, dict):
                        memory_item = {
                            "id": memory.get("id", "unknown"),
                            "memory": memory.get("memory", ""),
                            "score": memory.get("score", 0.0),
                            "categories": memory.get("categories", []),
                            "created_at": memory.get("created_at", ""),
                            "metadata": memory.get("metadata", {}),
                            "user_id": memory.get("user_id", ""),
                        }
                        processed_memories.append(memory_item)
                
                # 生成最终结果
                result = {
                    "query": payload["query"],
                    "api_version": "v2",
                    "memories": processed_memories,
                    "pagination": pagination_info,
                    "total_found": len(processed_memories)
                }
                
                print(f"\n📊 处理结果:")
                print(f"  API返回记忆数量: {len(memories)}")
                print(f"  处理后记忆数量: {len(processed_memories)}")
                print(f"  分页信息: {pagination_info}")
                
                # 生成Dify输出格式
                if processed_memories:
                    text_response = f"Query: {payload['query']} (V2 API)\n\nFound {len(processed_memories)} memories:\n\n"
                    for idx, memory in enumerate(processed_memories[:3], 1):
                        text_response += f"{idx}. Memory: {memory['memory']}\n"
                        text_response += f"   Score: {memory['score']:.3f}\n"
                else:
                    text_response = f"Query: {payload['query']} (V2 API)\n\nNo memories found."
                
                text_response += f"\n\nPagination: {pagination_info}"
                
                final_output = {
                    "text": text_response,
                    "files": [],
                    "json": [result]
                }
                
                print(f"\n📤 最终Dify输出:")
                print(json.dumps(final_output, indent=2, ensure_ascii=False))
                
                # 判断是否解决了问题
                if len(processed_memories) > 0:
                    print(f"\n🎉 原始问题已解决！")
                    print(f"✅ 成功找到 {len(processed_memories)} 个关于'小红'的记忆")
                    return True
                else:
                    print(f"\n❌ 原始问题仍然存在")
                    print(f"⚠️ total_count={pagination_info.get('total_count', 0)} 但 memories=0")
                    return False
                    
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return False

async def main():
    """主测试函数"""
    
    print("🚀 开始类型安全修复测试")
    print("=" * 60)
    
    await test_type_safety()
    success = await test_original_problem()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 类型安全修复成功！")
        print("✅ 原始问题已解决")
        print("✅ 支持布尔值和字符串类型的参数")
    else:
        print("💥 修复验证失败")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        print(f"\n{'✅ 测试通过' if result else '❌ 测试失败'}")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
