#!/usr/bin/env python3
"""
快速调试Dify搜索问题
"""

import json
import httpx
import asyncio

async def quick_debug():
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 1. 创建测试数据
        print("📝 创建测试数据...")
        test_memory = {
            "messages": [{"role": "user", "content": "我的名字是Alice，我是一名软件工程师"}],
            "user_id": "alice",
            "metadata": {"name": "Alice", "profession": "software engineer"}
        }
        
        response = await client.post("http://localhost:8000/v1/memories/", json=test_memory)
        if response.status_code == 200:
            result = response.json()
            memory_id = result[0]["id"] if result else None
            print(f"✅ 创建成功: {memory_id}")
        else:
            print(f"❌ 创建失败: {response.status_code}")
            return
        
        await asyncio.sleep(2)  # 等待索引
        
        # 2. 测试V2搜索API
        print("\n🔍 测试V2搜索API...")
        search_payload = {
            "query": "叫什么名字？",
            "user_id": "alice"
        }
        
        response = await client.post(
            "http://localhost:8000/v2/memories/search/",
            json=search_payload
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应结构: {list(data.keys())}")
            
            # 检查Dify代码期望的格式
            if "memories" in data:
                print(f"✅ 有memories字段: {len(data['memories'])} 个")
            else:
                print("❌ 没有memories字段")
            
            # 检查实际格式
            if "results" in data:
                results = data["results"]
                if isinstance(results, dict) and "results" in results:
                    actual_memories = results["results"]
                    print(f"✅ 实际数据在results.results: {len(actual_memories)} 个")
                    if actual_memories:
                        print(f"第一个记忆: {actual_memories[0].get('memory', 'N/A')}")
        
        # 3. 清理
        if memory_id:
            await client.delete(f"http://localhost:8000/v1/memories/{memory_id}/")
            print("🧹 清理完成")

if __name__ == "__main__":
    asyncio.run(quick_debug())
