#!/usr/bin/env python3
"""
调试当前的搜索问题
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

async def debug_current_issue():
    """调试当前问题"""
    
    print("🔍 调试当前搜索问题")
    print("=" * 40)
    
    # 用户的实际输入参数
    user_input = {
        "query": "小红",
        "user_id": "root",
        "score_range": "",
        "date_range": "",
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    
    print("📋 用户输入:")
    print(json.dumps(user_input, indent=2, ensure_ascii=False))
    
    # 模拟修复后的Dify参数处理
    payload = {"query": user_input["query"]}
    
    if user_input.get("user_id"):
        payload["user_id"] = user_input["user_id"]
    
    # 添加limit
    payload["limit"] = 10
    
    # 处理过滤器（修复后的逻辑）
    filters_dict = {}
    
    score_range = user_input.get("score_range")
    if score_range and score_range.strip():
        filters_dict["score_range"] = score_range

    date_range = user_input.get("date_range")
    if date_range and date_range.strip():
        filters_dict["date_range"] = date_range

    if filters_dict:
        payload["filters"] = filters_dict
    
    # 处理图谱参数（修复后的逻辑）
    enable_graph = user_input.get("enable_graph")
    if enable_graph and enable_graph.strip() and enable_graph.lower() != "false":
        payload["enable_graph"] = enable_graph

    graph_entities = user_input.get("graph_entities")
    if graph_entities and graph_entities.strip():
        payload["graph_entities"] = graph_entities

    relationship_filter = user_input.get("relationship_filter")
    if relationship_filter and relationship_filter.strip():
        payload["relationship_filter"] = relationship_filter
    
    print(f"\n📤 实际发送的payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    # 发送API请求
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/v2/memories/search/",
                json=payload
            )
            
            print(f"\n📥 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                
                print(f"📄 完整API响应:")
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                # 模拟Dify的处理逻辑
                print(f"\n🔄 模拟Dify处理逻辑:")
                
                memories = []
                pagination_info = {}
                
                if isinstance(response_data, dict):
                    print(f"✅ 响应是字典类型")
                    
                    # 检查results字段
                    if "results" in response_data:
                        results = response_data["results"]
                        print(f"✅ 找到results字段: {type(results)}")
                        
                        if isinstance(results, dict):
                            print(f"✅ results是字典类型")
                            
                            if "results" in results:
                                memories = results["results"]
                                print(f"✅ 从results.results提取到 {len(memories)} 个记忆")
                            else:
                                print(f"❌ results字典中没有results字段")
                                print(f"   可用字段: {list(results.keys())}")
                        else:
                            print(f"❌ results不是字典类型: {type(results)}")
                    else:
                        print(f"❌ 响应中没有results字段")
                        print(f"   可用字段: {list(response_data.keys())}")
                    
                    # 检查分页信息
                    if "total_count" in response_data:
                        pagination_info["total_count"] = response_data["total_count"]
                        print(f"✅ 找到total_count: {response_data['total_count']}")
                    
                    if "limit" in response_data:
                        pagination_info["limit"] = response_data["limit"]
                        print(f"✅ 找到limit: {response_data['limit']}")
                
                # 处理记忆数据
                processed_memories = []
                for memory in memories:
                    if isinstance(memory, dict):
                        memory_item = {
                            "id": memory.get("id", "unknown"),
                            "memory": memory.get("memory", ""),
                            "score": memory.get("score", 0.0),
                            "categories": memory.get("categories", []),
                            "created_at": memory.get("created_at", ""),
                            "updated_at": memory.get("updated_at", ""),
                            "metadata": memory.get("metadata", {}),
                            "user_id": memory.get("user_id", ""),
                            "agent_id": memory.get("agent_id", ""),
                            "run_id": memory.get("run_id", "")
                        }
                        processed_memories.append(memory_item)
                
                print(f"\n🎯 最终处理结果:")
                print(f"  原始记忆数量: {len(memories)}")
                print(f"  处理后记忆数量: {len(processed_memories)}")
                print(f"  分页信息: {pagination_info}")
                
                # 生成最终的Dify输出
                result = {
                    "query": payload["query"],
                    "api_version": "v2",
                    "memories": processed_memories,
                    "pagination": pagination_info,
                    "total_found": len(processed_memories)
                }
                
                text_response = f"Query: {payload['query']} (V2 API)\n\n"
                
                if processed_memories:
                    text_response += f"Found {len(processed_memories)} memories:\n"
                    for idx, memory in enumerate(processed_memories, 1):
                        text_response += f"\n{idx}. Memory: {memory['memory']}"
                        text_response += f"\n   Score: {memory['score']:.3f}"
                        if memory['categories']:
                            text_response += f"\n   Categories: {', '.join(memory['categories'])}"
                else:
                    text_response += "No memories found."
                
                if pagination_info:
                    text_response += f"\n\nPagination: {pagination_info}"
                
                print(f"\n📤 最终Dify输出:")
                final_output = {
                    "text": text_response,
                    "files": [],
                    "json": [result]
                }
                print(json.dumps(final_output, indent=2, ensure_ascii=False))
                
                # 分析问题
                if len(processed_memories) == 0 and pagination_info.get("total_count", 0) > 0:
                    print(f"\n❌ 发现问题：total_count > 0 但没有记忆数据")
                    print(f"   这表明API返回了数据但Dify处理逻辑有问题")
                elif len(processed_memories) > 0:
                    print(f"\n✅ 问题已解决：成功提取到记忆数据")
                else:
                    print(f"\n⚠️ API本身没有返回数据")
                
            else:
                print(f"❌ API请求失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def main():
    try:
        await debug_current_issue()
        print("\n✅ 调试完成")
    except Exception as e:
        print(f"\n💥 调试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
