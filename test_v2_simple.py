#!/usr/bin/env python3
"""
简化的V2 API测试脚本
专门测试V2 API的搜索和获取功能
"""

import asyncio
import json
import logging
import sys
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_v2_simple"

async def test_v2_apis():
    """测试V2 API功能"""
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 1. 创建测试数据
        logger.info("📝 创建测试数据...")
        test_memory = {
            "messages": [{"role": "user", "content": "我喜欢吃中式料理，特别是川菜"}],
            "user_id": TEST_USER_ID,
            "metadata": {"category": "food", "cuisine": "chinese"}
        }
        
        response = await client.post(f"{BASE_URL}/v1/memories/", json=test_memory)
        if response.status_code == 200:
            result = response.json()
            memory_id = result[0]["id"] if result else None
            logger.info(f"✅ 创建测试记忆成功: {memory_id}")
        else:
            logger.error(f"❌ 创建测试记忆失败: {response.status_code} - {response.text}")
            return False
        
        # 等待索引完成
        await asyncio.sleep(2)
        
        # 2. 测试V2获取API
        logger.info("🔍 测试V2获取API...")
        get_payload = {
            "user_id": TEST_USER_ID,
            "limit": 10
        }
        
        response = await client.post(f"{BASE_URL}/v2/memories/", json=get_payload)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ V2获取API成功: 返回 {len(data)} 个记忆")
            logger.info(f"📄 响应格式: {type(data)} - {list(data.keys()) if isinstance(data, dict) else 'list'}")
        else:
            logger.error(f"❌ V2获取API失败: {response.status_code} - {response.text}")
        
        # 3. 测试V2搜索API
        logger.info("🔍 测试V2搜索API...")
        search_payload = {
            "query": "喜欢吃什么",
            "user_id": TEST_USER_ID,
            "limit": 5
        }
        
        response = await client.post(f"{BASE_URL}/v2/memories/search/", json=search_payload)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ V2搜索API成功")
            logger.info(f"📄 响应格式: {type(data)}")
            
            # 分析响应结构
            if isinstance(data, dict):
                logger.info(f"📋 响应字段: {list(data.keys())}")
                if "results" in data:
                    results = data["results"]
                    if isinstance(results, dict) and "results" in results:
                        memories = results["results"]
                        logger.info(f"🎯 找到 {len(memories)} 个相关记忆")
                        for i, memory in enumerate(memories[:2]):  # 显示前2个
                            logger.info(f"  记忆{i+1}: {memory.get('memory', 'N/A')} (分数: {memory.get('score', 'N/A')})")
                    else:
                        logger.info(f"📋 results字段内容: {results}")
                else:
                    logger.info(f"📋 完整响应: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
        else:
            logger.error(f"❌ V2搜索API失败: {response.status_code} - {response.text}")
        
        # 4. 测试复杂过滤器
        logger.info("🔍 测试复杂过滤器...")
        filter_payload = {
            "user_id": TEST_USER_ID,
            "filters": {
                "AND": [
                    {"metadata.category": "food"}
                ]
            },
            "limit": 10
        }
        
        response = await client.post(f"{BASE_URL}/v2/memories/", json=filter_payload)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ 复杂过滤器成功: 返回 {len(data)} 个记忆")
        else:
            logger.error(f"❌ 复杂过滤器失败: {response.status_code} - {response.text}")
        
        # 5. 测试搜索过滤器组合
        logger.info("🔍 测试搜索+过滤器组合...")
        search_filter_payload = {
            "query": "料理",
            "user_id": TEST_USER_ID,
            "filters": {
                "AND": [
                    {"metadata.cuisine": "chinese"}
                ]
            },
            "limit": 5
        }
        
        response = await client.post(f"{BASE_URL}/v2/memories/search/", json=search_filter_payload)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ 搜索+过滤器组合成功")
            
            # 提取实际的记忆数据
            if isinstance(data, dict) and "results" in data:
                results = data["results"]
                if isinstance(results, dict) and "results" in results:
                    memories = results["results"]
                    logger.info(f"🎯 组合搜索找到 {len(memories)} 个相关记忆")
        else:
            logger.error(f"❌ 搜索+过滤器组合失败: {response.status_code} - {response.text}")
        
        # 6. 清理测试数据
        logger.info("🧹 清理测试数据...")
        if memory_id:
            response = await client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            if response.status_code == 200:
                logger.info("✅ 测试数据清理完成")
            else:
                logger.warning(f"⚠️ 清理测试数据失败: {response.status_code}")
        
        logger.info("🎉 V2 API测试完成！")
        return True

async def main():
    """主函数"""
    logger.info("🚀 开始V2 API简化测试")
    logger.info("=" * 50)
    
    try:
        success = await test_v2_apis()
        if success:
            logger.info("✅ 测试完成")
            return 0
        else:
            logger.error("❌ 测试失败")
            return 1
    except Exception as e:
        logger.error(f"💥 测试异常: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
