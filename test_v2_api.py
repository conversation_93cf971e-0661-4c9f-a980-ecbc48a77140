#!/usr/bin/env python3
"""
V2 API 搜索和获取功能测试脚本

测试内容：
1. V2 API 获取memories功能 (/v2/memories/)
2. V2 API 搜索memories功能 (/v2/memories/search/)
3. 复杂过滤器功能测试
4. 通配符功能测试
5. 比较操作符测试
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

import httpx

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_v2_api"
TEST_AGENT_ID = "test_agent_v2_api"
TEST_RUN_ID = "test_run_v2_api"

class V2APITester:
    """V2 API测试类"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_memory_ids = []
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await self.client.aclose()
    
    async def cleanup(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        for memory_id in self.test_memory_ids:
            try:
                response = await self.client.delete(f"{self.base_url}/v1/memories/{memory_id}/")
                if response.status_code == 200:
                    logger.info(f"✅ 删除测试记忆: {memory_id}")
            except Exception as e:
                logger.warning(f"⚠️ 删除记忆失败 {memory_id}: {e}")
    
    async def setup_test_data(self) -> List[str]:
        """设置测试数据"""
        logger.info("📝 设置测试数据...")
        
        test_memories = [
            {
                "messages": [{"role": "user", "content": "我喜欢吃披萨，特别是意大利香肠披萨"}],
                "user_id": TEST_USER_ID,
                "metadata": {"category": "food", "preference": "pizza"}
            },
            {
                "messages": [{"role": "user", "content": "我每周末都会去健身房锻炼"}],
                "user_id": TEST_USER_ID,
                "metadata": {"category": "fitness", "frequency": "weekly"}
            },
            {
                "messages": [{"role": "user", "content": "我在一家科技公司工作，主要做Python开发"}],
                "user_id": TEST_USER_ID,
                "agent_id": TEST_AGENT_ID,
                "metadata": {"category": "work", "skill": "python"}
            },
            {
                "messages": [{"role": "user", "content": "我计划明年去日本旅行"}],
                "user_id": TEST_USER_ID,
                "run_id": TEST_RUN_ID,
                "metadata": {"category": "travel", "destination": "japan"}
            },
            {
                "messages": [{"role": "user", "content": "我喜欢看科幻电影，特别是星际穿越"}],
                "agent_id": TEST_AGENT_ID,
                "metadata": {"category": "entertainment", "genre": "sci-fi"}
            }
        ]
        
        memory_ids = []
        for i, memory_data in enumerate(test_memories):
            try:
                response = await self.client.post(
                    f"{self.base_url}/v1/memories/",
                    json=memory_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if isinstance(result, list) and len(result) > 0:
                        memory_id = result[0].get("id")
                        if memory_id:
                            memory_ids.append(memory_id)
                            logger.info(f"✅ 创建测试记忆 {i+1}: {memory_id}")
                        else:
                            logger.warning(f"⚠️ 记忆创建成功但未返回ID: {result}")
                    else:
                        logger.warning(f"⚠️ 记忆创建响应格式异常: {result}")
                else:
                    logger.error(f"❌ 创建测试记忆失败 {i+1}: {response.status_code} - {response.text}")
                    
            except Exception as e:
                logger.error(f"❌ 创建测试记忆异常 {i+1}: {e}")
        
        self.test_memory_ids = memory_ids
        logger.info(f"📊 成功创建 {len(memory_ids)} 个测试记忆")
        
        # 等待数据索引完成
        await asyncio.sleep(2)
        return memory_ids
    
    async def test_v2_get_memories_basic(self):
        """测试V2 API基本获取功能"""
        logger.info("🔍 测试V2 API基本获取功能...")
        
        test_cases = [
            {
                "name": "按user_id获取",
                "payload": {"user_id": TEST_USER_ID, "limit": 10}
            },
            {
                "name": "按agent_id获取", 
                "payload": {"agent_id": TEST_AGENT_ID, "limit": 10}
            },
            {
                "name": "按run_id获取",
                "payload": {"run_id": TEST_RUN_ID, "limit": 10}
            }
        ]
        
        results = {}
        for case in test_cases:
            try:
                response = await self.client.post(
                    f"{self.base_url}/v2/memories/",
                    json=case["payload"]
                )
                
                if response.status_code == 200:
                    data = response.json()
                    memory_count = len(data) if isinstance(data, list) else 0
                    results[case["name"]] = {
                        "success": True,
                        "count": memory_count,
                        "data": data
                    }
                    logger.info(f"✅ {case['name']}: 获取到 {memory_count} 个记忆")
                else:
                    results[case["name"]] = {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
                    logger.error(f"❌ {case['name']}: {response.status_code} - {response.text}")
                    
            except Exception as e:
                results[case["name"]] = {
                    "success": False,
                    "error": str(e)
                }
                logger.error(f"❌ {case['name']} 异常: {e}")
        
        return results
    
    async def test_v2_get_memories_complex_filters(self):
        """测试V2 API复杂过滤器功能"""
        logger.info("🔍 测试V2 API复杂过滤器功能...")

        test_cases = [
            {
                "name": "AND逻辑过滤器",
                "payload": {
                    "user_id": TEST_USER_ID,  # 必须提供身份标识符
                    "filters": {
                        "AND": [
                            {"metadata.category": "food"}
                        ]
                    },
                    "limit": 10
                }
            },
            {
                "name": "OR逻辑过滤器",
                "payload": {
                    "user_id": TEST_USER_ID,  # 必须提供身份标识符
                    "filters": {
                        "OR": [
                            {"metadata.category": "food"},
                            {"metadata.category": "work"}
                        ]
                    },
                    "limit": 10
                }
            },
            {
                "name": "通配符过滤器",
                "payload": {
                    "user_id": TEST_USER_ID,
                    "filters": {
                        "AND": [
                            {"run_id": "*"}
                        ]
                    },
                    "limit": 10
                }
            }
        ]
        
        results = {}
        for case in test_cases:
            try:
                response = await self.client.post(
                    f"{self.base_url}/v2/memories/",
                    json=case["payload"]
                )
                
                if response.status_code == 200:
                    data = response.json()
                    memory_count = len(data) if isinstance(data, list) else 0
                    results[case["name"]] = {
                        "success": True,
                        "count": memory_count,
                        "data": data
                    }
                    logger.info(f"✅ {case['name']}: 获取到 {memory_count} 个记忆")
                else:
                    results[case["name"]] = {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
                    logger.error(f"❌ {case['name']}: {response.status_code} - {response.text}")
                    
            except Exception as e:
                results[case["name"]] = {
                    "success": False,
                    "error": str(e)
                }
                logger.error(f"❌ {case['name']} 异常: {e}")
        
        return results
    
    async def test_v2_search_memories_basic(self):
        """测试V2 API基本搜索功能"""
        logger.info("🔍 测试V2 API基本搜索功能...")
        
        test_cases = [
            {
                "name": "搜索食物偏好",
                "payload": {
                    "query": "喜欢吃什么食物",
                    "user_id": TEST_USER_ID,
                    "limit": 5
                }
            },
            {
                "name": "搜索工作信息",
                "payload": {
                    "query": "工作和技能",
                    "user_id": TEST_USER_ID,
                    "limit": 5
                }
            },
            {
                "name": "搜索娱乐偏好",
                "payload": {
                    "query": "电影偏好",
                    "agent_id": TEST_AGENT_ID,
                    "limit": 5
                }
            }
        ]
        
        results = {}
        for case in test_cases:
            try:
                response = await self.client.post(
                    f"{self.base_url}/v2/memories/search/",
                    json=case["payload"]
                )
                
                if response.status_code == 200:
                    data = response.json()
                    # V2 API实际返回格式: {"results": {"results": [...], "relations": []}, ...}
                    if isinstance(data, dict) and "results" in data:
                        results_data = data["results"]
                        if isinstance(results_data, dict) and "results" in results_data:
                            memories = results_data["results"]
                            memory_count = len(memories)
                            results[case["name"]] = {
                                "success": True,
                                "count": memory_count,
                                "data": data
                            }
                            logger.info(f"✅ {case['name']}: 搜索到 {memory_count} 个相关记忆")
                        else:
                            results[case["name"]] = {
                                "success": False,
                                "error": f"results字段格式异常: {results_data}"
                            }
                            logger.error(f"❌ {case['name']}: results字段格式异常")
                    else:
                        results[case["name"]] = {
                            "success": False,
                            "error": f"响应格式异常: {data}"
                        }
                        logger.error(f"❌ {case['name']}: 响应格式异常")
                else:
                    results[case["name"]] = {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
                    logger.error(f"❌ {case['name']}: {response.status_code} - {response.text}")
                    
            except Exception as e:
                results[case["name"]] = {
                    "success": False,
                    "error": str(e)
                }
                logger.error(f"❌ {case['name']} 异常: {e}")
        
        return results

async def main():
    """主测试函数"""
    logger.info("🚀 开始V2 API搜索和获取功能测试")
    logger.info("=" * 60)
    
    async with V2APITester() as tester:
        # 设置测试数据
        memory_ids = await tester.setup_test_data()
        if not memory_ids:
            logger.error("❌ 测试数据设置失败，退出测试")
            return False
        
        # 执行测试
        test_results = {}
        
        # 测试V2获取功能
        test_results["v2_get_basic"] = await tester.test_v2_get_memories_basic()
        test_results["v2_get_complex"] = await tester.test_v2_get_memories_complex_filters()
        
        # 测试V2搜索功能
        test_results["v2_search_basic"] = await tester.test_v2_search_memories_basic()
        
        # 输出测试结果摘要
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试结果摘要")
        logger.info("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in test_results.items():
            logger.info(f"\n📋 {category}:")
            for test_name, result in results.items():
                total_tests += 1
                if result["success"]:
                    passed_tests += 1
                    logger.info(f"  ✅ {test_name}: 通过 (获取 {result.get('count', 0)} 条记录)")
                else:
                    logger.error(f"  ❌ {test_name}: 失败 - {result.get('error', '未知错误')}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"\n🎯 总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 V2 API测试基本通过！")
            return True
        else:
            logger.error("💥 V2 API测试存在问题，需要进一步检查")
            return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 测试执行异常: {e}")
        sys.exit(1)
