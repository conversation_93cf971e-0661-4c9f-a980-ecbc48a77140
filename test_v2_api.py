#!/usr/bin/env python3
"""
V2 API 测试脚本
测试 mem0 V2 API 的搜索和获取功能，包括复杂过滤器和操作符
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

class V2APITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def _make_request(self, endpoint: str, method: str = "POST", data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        try:
            if method.upper() == "POST":
                response = self.session.post(url, json=data, headers=headers)
            else:
                response = self.session.get(url, headers=headers)
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return {"error": str(e)}
    
    def test_basic_search(self):
        """测试基本搜索功能"""
        print("\n=== 测试基本搜索功能 ===")
        
        # 基本搜索
        search_data = {
            "query": "What are Alice's hobbies?",
            "user_id": "alice",
            "limit": 10
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"基本搜索结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_complex_filters_and(self):
        """测试AND逻辑过滤器"""
        print("\n=== 测试AND逻辑过滤器 ===")
        
        search_data = {
            "query": "What are Alice's preferences?",
            "user_id": "alice",
            "filters": {
                "AND": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "agent_id": {"in": ["travel-agent", "sports-agent"]}
                    }
                ]
            },
            "limit": 5
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"AND过滤器结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_complex_filters_or(self):
        """测试OR逻辑过滤器"""
        print("\n=== 测试OR逻辑过滤器 ===")
        
        search_data = {
            "query": "What are Alice's hobbies?",
            "filters": {
                "OR": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "agent_id": {"in": ["travel-agent", "sports-agent"]}
                    }
                ]
            },
            "limit": 5
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"OR过滤器结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_comparison_operators(self):
        """测试比较操作符"""
        print("\n=== 测试比较操作符 ===")
        
        # 测试时间范围过滤
        now = datetime.now()
        week_ago = now - timedelta(days=7)
        
        search_data = {
            "query": "Recent memories",
            "user_id": "alice",
            "filters": {
                "AND": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "created_at": {
                            "gte": week_ago.isoformat(),
                            "lte": now.isoformat()
                        }
                    }
                ]
            },
            "limit": 10
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"时间范围过滤结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_wildcard_operator(self):
        """测试通配符操作符"""
        print("\n=== 测试通配符操作符 ===")
        
        search_data = {
            "query": "All memories for Alice",
            "filters": {
                "AND": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "run_id": "*"  # 匹配所有run_id
                    }
                ]
            },
            "limit": 10
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"通配符过滤结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_advanced_retrieval_options(self):
        """测试高级检索选项"""
        print("\n=== 测试高级检索选项 ===")
        
        search_data = {
            "query": "What are Alice's preferences?",
            "user_id": "alice",
            "keyword_search": True,
            "rerank": True,
            "filter_memories": True,
            "limit": 5
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"高级检索结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_get_memories_v2(self):
        """测试V2获取记忆功能"""
        print("\n=== 测试V2获取记忆功能 ===")
        
        # 基本获取
        get_data = {
            "user_id": "alice",
            "limit": 10
        }
        
        result = self._make_request("/v2/memories/", data=get_data)
        print(f"基本获取结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_get_memories_with_filters(self):
        """测试带过滤器的获取记忆功能"""
        print("\n=== 测试带过滤器的获取记忆功能 ===")
        
        get_data = {
            "user_id": "alex",
            "filters": {
                "AND": [
                    {
                        "user_id": "alex"
                    },
                    {
                        "created_at": {
                            "gte": "2024-07-01",
                            "lte": "2024-07-31"
                        }
                    }
                ]
            },
            "limit": 10
        }
        
        result = self._make_request("/v2/memories/", data=get_data)
        print(f"带过滤器的获取结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_icontains_operator(self):
        """测试icontains操作符"""
        print("\n=== 测试icontains操作符 ===")
        
        search_data = {
            "query": "Find memories containing specific text",
            "user_id": "alice",
            "filters": {
                "AND": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "memory": {
                            "icontains": "cricket"  # 不区分大小写包含
                        }
                    }
                ]
            },
            "limit": 5
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"icontains过滤结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_metadata_filtering(self):
        """测试元数据过滤"""
        print("\n=== 测试元数据过滤 ===")
        
        search_data = {
            "query": "Find memories with specific metadata",
            "user_id": "alice",
            "filters": {
                "AND": [
                    {
                        "user_id": "alice"
                    },
                    {
                        "metadata.category": {
                            "in": ["hobbies", "preferences"]
                        }
                    }
                ]
            },
            "limit": 5
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"元数据过滤结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        # 测试缺少必需参数
        search_data = {
            "query": "Test query"
            # 缺少 user_id, agent_id, 或 run_id
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"缺少必需参数的结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 测试无效的过滤器
        search_data = {
            "query": "Test query",
            "user_id": "alice",
            "filters": {
                "invalid_field": {
                    "invalid_operator": "invalid_value"
                }
            }
        }
        
        result = self._make_request("/v2/memories/search/", data=search_data)
        print(f"无效过滤器结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始V2 API测试...")
        print(f"测试服务器: {self.base_url}")
        
        tests = [
            ("基本搜索", self.test_basic_search),
            ("AND逻辑过滤器", self.test_complex_filters_and),
            ("OR逻辑过滤器", self.test_complex_filters_or),
            ("比较操作符", self.test_comparison_operators),
            ("通配符操作符", self.test_wildcard_operator),
            ("高级检索选项", self.test_advanced_retrieval_options),
            ("V2获取记忆", self.test_get_memories_v2),
            ("带过滤器的获取", self.test_get_memories_with_filters),
            ("icontains操作符", self.test_icontains_operator),
            ("元数据过滤", self.test_metadata_filtering),
            ("错误处理", self.test_error_handling)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*50}")
                print(f"执行测试: {test_name}")
                print(f"{'='*50}")
                
                start_time = time.time()
                result = test_func()
                end_time = time.time()
                
                results[test_name] = {
                    "status": "success" if "error" not in result else "error",
                    "duration": end_time - start_time,
                    "result": result
                }
                
                print(f"测试完成: {test_name} (耗时: {end_time - start_time:.2f}秒)")
                
            except Exception as e:
                print(f"测试失败: {test_name} - {str(e)}")
                results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # 生成测试报告
        self._generate_test_report(results)
        
        return results
    
    def _generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        print("\n" + "="*60)
        print("V2 API 测试报告")
        print("="*60)
        
        successful_tests = []
        failed_tests = []
        
        for test_name, result in results.items():
            if result["status"] == "success":
                successful_tests.append(test_name)
            else:
                failed_tests.append(test_name)
        
        print(f"总测试数: {len(results)}")
        print(f"成功: {len(successful_tests)}")
        print(f"失败: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(results)*100:.1f}%")
        
        if successful_tests:
            print(f"\n成功的测试:")
            for test in successful_tests:
                duration = results[test]["duration"]
                print(f"  ✓ {test} ({duration:.2f}s)")
        
        if failed_tests:
            print(f"\n失败的测试:")
            for test in failed_tests:
                error = results[test].get("error", "未知错误")
                print(f"  ✗ {test}: {error}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    # 创建测试器实例
    tester = V2APITester()
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 返回结果供进一步分析
    return results

if __name__ == "__main__":
    main() 