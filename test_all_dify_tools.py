#!/usr/bin/env python3
"""
测试所有Dify插件工具的功能
"""

import asyncio
import json
import logging
import sys
from typing import Dict, Any, List
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"
TEST_USER_ID = "dify_test_user"
TEST_AGENT_ID = "dify_test_agent"

class DifyToolsTester:
    """Dify工具测试类"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_memory_ids = []
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await self.client.aclose()
    
    async def cleanup(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        for memory_id in self.test_memory_ids:
            try:
                await self.client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
                logger.info(f"✅ 删除测试记忆: {memory_id}")
            except Exception as e:
                logger.warning(f"⚠️ 删除记忆失败 {memory_id}: {e}")
    
    async def test_add_memory(self):
        """测试添加记忆工具"""
        logger.info("🔍 测试添加记忆工具...")

        # 模拟Dify工具的添加记忆请求 - 使用正确的格式
        payload = {
            "messages": [
                {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁"}
            ],
            "user_id": TEST_USER_ID,
            "metadata": {"preference": "coffee", "type": "latte"}  # 直接使用dict而不是JSON字符串
        }
        
        try:
            response = await self.client.post(f"{BASE_URL}/v1/memories/", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0:
                    memory_id = result[0].get("id")
                    if memory_id:
                        self.test_memory_ids.append(memory_id)
                        logger.info(f"✅ 添加记忆成功: {memory_id}")
                        return {"success": True, "memory_id": memory_id, "data": result}
                    else:
                        logger.error("❌ 添加记忆成功但未返回ID")
                        return {"success": False, "error": "No memory ID returned"}
                else:
                    logger.error("❌ 添加记忆返回空结果")
                    return {"success": False, "error": "Empty result"}
            else:
                logger.error(f"❌ 添加记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 添加记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_search_memories(self):
        """测试搜索记忆工具（已修复）"""
        logger.info("🔍 测试搜索记忆工具...")
        
        # 等待索引完成
        await asyncio.sleep(2)
        
        payload = {
            "query": "喜欢喝什么",
            "user_id": TEST_USER_ID,
            "limit": 5
        }
        
        try:
            response = await self.client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                
                # 使用修复后的逻辑处理响应
                memories = []
                if isinstance(data, dict):
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            memories = data["results"]["results"]
                    elif "memories" in data:
                        memories = data["memories"]
                
                logger.info(f"✅ 搜索记忆成功: 找到 {len(memories)} 个相关记忆")
                
                if memories:
                    for i, memory in enumerate(memories[:2], 1):
                        logger.info(f"  记忆{i}: {memory.get('memory', 'N/A')} (分数: {memory.get('score', 'N/A')})")
                
                return {"success": True, "count": len(memories), "memories": memories}
            else:
                logger.error(f"❌ 搜索记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 搜索记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_list_memories(self):
        """测试列出记忆工具"""
        logger.info("🔍 测试列出记忆工具...")
        
        payload = {
            "user_id": TEST_USER_ID,
            "limit": 10
        }
        
        try:
            response = await self.client.get(
                f"{BASE_URL}/v1/memories/",
                params=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get("results", []) if isinstance(data, dict) else []
                
                logger.info(f"✅ 列出记忆成功: 找到 {len(memories)} 个记忆")
                return {"success": True, "count": len(memories), "memories": memories}
            else:
                logger.error(f"❌ 列出记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 列出记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_update_memory(self):
        """测试更新记忆工具"""
        logger.info("🔍 测试更新记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可更新的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids[0]
        payload = {
            "text": "我喜欢喝咖啡，特别是卡布奇诺（已更新）"
        }
        
        try:
            response = await self.client.put(
                f"{BASE_URL}/v1/memories/{memory_id}/",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 更新记忆成功: {memory_id}")
                return {"success": True, "memory_id": memory_id, "data": result}
            else:
                logger.error(f"❌ 更新记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 更新记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_delete_memory(self):
        """测试删除记忆工具"""
        logger.info("🔍 测试删除记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可删除的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids.pop()  # 取出一个ID进行删除测试
        
        try:
            response = await self.client.delete(f"{BASE_URL}/v1/memories/{memory_id}/")
            
            if response.status_code == 200:
                logger.info(f"✅ 删除记忆成功: {memory_id}")
                return {"success": True, "memory_id": memory_id}
            else:
                logger.error(f"❌ 删除记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 删除记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_get_memory(self):
        """测试获取单个记忆工具"""
        logger.info("🔍 测试获取单个记忆工具...")
        
        if not self.test_memory_ids:
            logger.warning("⚠️ 没有可获取的记忆ID")
            return {"success": False, "error": "No memory ID available"}
        
        memory_id = self.test_memory_ids[0]
        
        try:
            response = await self.client.get(f"{BASE_URL}/v1/memories/{memory_id}/")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 获取记忆成功: {memory_id}")
                logger.info(f"  内容: {result.get('memory', 'N/A')}")
                return {"success": True, "memory_id": memory_id, "data": result}
            else:
                logger.error(f"❌ 获取记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ 获取记忆异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_v2_get_memories(self):
        """测试V2获取记忆工具"""
        logger.info("🔍 测试V2获取记忆工具...")
        
        payload = {
            "user_id": TEST_USER_ID,
            "limit": 10
        }
        
        try:
            response = await self.client.post(f"{BASE_URL}/v2/memories/", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get("memories", []) if isinstance(data, dict) else []
                
                logger.info(f"✅ V2获取记忆成功: 找到 {len(memories)} 个记忆")
                return {"success": True, "count": len(memories), "memories": memories}
            else:
                logger.error(f"❌ V2获取记忆失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.error(f"❌ V2获取记忆异常: {e}")
            return {"success": False, "error": str(e)}

async def main():
    """主测试函数"""
    logger.info("🚀 开始测试所有Dify插件工具")
    logger.info("=" * 60)
    
    async with DifyToolsTester() as tester:
        # 执行所有测试
        test_results = {}
        
        # 1. 测试添加记忆
        test_results["add_memory"] = await tester.test_add_memory()
        
        # 2. 测试搜索记忆
        test_results["search_memories"] = await tester.test_search_memories()
        
        # 3. 测试列出记忆
        test_results["list_memories"] = await tester.test_list_memories()
        
        # 4. 测试V2获取记忆
        test_results["v2_get_memories"] = await tester.test_v2_get_memories()
        
        # 5. 测试获取单个记忆
        test_results["get_memory"] = await tester.test_get_memory()
        
        # 6. 测试更新记忆
        test_results["update_memory"] = await tester.test_update_memory()
        
        # 7. 测试删除记忆
        test_results["delete_memory"] = await tester.test_delete_memory()
        
        # 输出测试结果摘要
        logger.info("\n" + "=" * 60)
        logger.info("📊 Dify工具测试结果摘要")
        logger.info("=" * 60)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for tool_name, result in test_results.items():
            if result["success"]:
                passed_tests += 1
                logger.info(f"✅ {tool_name}: 通过")
            else:
                logger.error(f"❌ {tool_name}: 失败 - {result.get('error', '未知错误')}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"\n🎯 总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 Dify工具测试基本通过！")
            return True
        else:
            logger.error("💥 Dify工具测试存在问题，需要进一步检查")
            return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 测试执行异常: {e}")
        sys.exit(1)
