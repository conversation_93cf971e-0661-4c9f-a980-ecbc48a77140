#!/usr/bin/env python3
"""
最终验证修复效果
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

async def final_verification():
    """最终验证"""
    
    print("🎯 最终验证修复效果")
    print("=" * 40)
    
    # 用户报告的问题输入
    problem_input = {
        "query": "小红",
        "user_id": "root",
        "score_range": "",
        "date_range": "",
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    
    print("📋 问题输入参数:")
    print(json.dumps(problem_input, indent=2, ensure_ascii=False))
    
    # 模拟修复后的处理逻辑
    def process_parameters(params):
        payload = {
            "query": params.get("query", ""),
            "user_id": params.get("user_id", ""),
            "limit": 10
        }
        
        filters_dict = {}
        
        # 修复后的过滤器处理
        score_range = params.get("score_range")
        if score_range and score_range.strip():
            filters_dict["score_range"] = score_range

        date_range = params.get("date_range")
        if date_range and date_range.strip():
            filters_dict["date_range"] = date_range

        category_filter = params.get("category_filter")
        if category_filter and category_filter.strip():
            filters_dict["categories"] = category_filter
        
        if filters_dict:
            payload["filters"] = filters_dict
        
        # 修复后的图谱参数处理
        enable_graph = params.get("enable_graph")
        if enable_graph and enable_graph.strip() and enable_graph.lower() != "false":
            payload["enable_graph"] = enable_graph

        graph_entities = params.get("graph_entities")
        if graph_entities and graph_entities.strip():
            payload["graph_entities"] = graph_entities

        relationship_filter = params.get("relationship_filter")
        if relationship_filter and relationship_filter.strip():
            payload["relationship_filter"] = relationship_filter
        
        return payload
    
    payload = process_parameters(problem_input)
    print(f"\n📤 修复后的payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    # 测试API调用
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/v2/memories/search/",
                json=payload
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 模拟Dify的完整处理流程
                memories = []
                pagination_info = {}
                
                if isinstance(response_data, dict):
                    # 处理V2 API的实际响应格式
                    if "results" in response_data and isinstance(response_data["results"], dict):
                        if "results" in response_data["results"]:
                            memories = response_data["results"]["results"]
                    
                    # 构建分页信息
                    if "total_count" in response_data:
                        pagination_info["total_count"] = response_data["total_count"]
                    if "limit" in response_data:
                        pagination_info["limit"] = response_data["limit"]
                
                # 处理记忆数据
                processed_memories = []
                for memory in memories:
                    if isinstance(memory, dict):
                        memory_item = {
                            "id": memory.get("id", "unknown"),
                            "memory": memory.get("memory", ""),
                            "score": memory.get("score", 0.0),
                            "categories": memory.get("categories", []),
                            "created_at": memory.get("created_at", ""),
                            "updated_at": memory.get("updated_at", ""),
                            "metadata": memory.get("metadata", {}),
                            "user_id": memory.get("user_id", ""),
                            "agent_id": memory.get("agent_id", ""),
                            "run_id": memory.get("run_id", "")
                        }
                        processed_memories.append(memory_item)
                
                # 生成最终结果
                result = {
                    "query": payload["query"],
                    "api_version": "v2",
                    "memories": processed_memories,
                    "pagination": pagination_info,
                    "total_found": len(processed_memories)
                }
                
                # 生成文本响应
                text_response = f"Query: {payload['query']} (V2 API)\n\n"
                
                if processed_memories:
                    text_response += f"Found {len(processed_memories)} memories:\n"
                    for idx, memory in enumerate(processed_memories[:3], 1):  # 只显示前3个
                        text_response += f"\n{idx}. Memory: {memory['memory']}"
                        text_response += f"\n   Score: {memory['score']:.3f}"
                        if memory['categories']:
                            text_response += f"\n   Categories: {', '.join(memory['categories'])}"
                        text_response += f"\n   Created: {memory['created_at']}"
                    
                    if len(processed_memories) > 3:
                        text_response += f"\n\n... and {len(processed_memories) - 3} more memories"
                else:
                    text_response += "No memories found."
                
                if pagination_info:
                    text_response += f"\n\nPagination: {pagination_info}"
                
                # 最终输出
                final_output = {
                    "text": text_response,
                    "files": [],
                    "json": [result]
                }
                
                print(f"\n📊 处理结果:")
                print(f"  找到记忆: {len(processed_memories)} 个")
                print(f"  分页信息: {pagination_info}")
                
                print(f"\n📤 修复后的完整输出:")
                print(json.dumps(final_output, indent=2, ensure_ascii=False))
                
                # 对比用户报告的问题
                print(f"\n🔍 问题对比:")
                print(f"  用户报告: memories=[], total_found=0")
                print(f"  修复后: memories={len(processed_memories)}个, total_found={len(processed_memories)}")
                
                if len(processed_memories) > 0:
                    print(f"\n🎉 问题已解决！")
                    print(f"✅ 成功找到 {len(processed_memories)} 个关于'小红'的记忆")
                    print(f"✅ 修复生效，空字符串参数不再干扰搜索")
                    return True
                else:
                    print(f"\n❌ 问题仍然存在")
                    return False
                    
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 调用异常: {e}")
            return False

async def test_edge_cases():
    """测试边界情况"""
    
    print(f"\n🧪 测试边界情况")
    print("=" * 30)
    
    edge_cases = [
        {
            "name": "所有参数都是空字符串",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "",
                "date_range": "",
                "enable_graph": "",
                "graph_entities": "",
                "relationship_filter": ""
            }
        },
        {
            "name": "enable_graph为false（小写）",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": "false"
            }
        },
        {
            "name": "enable_graph为FALSE（大写）",
            "params": {
                "query": "小红",
                "user_id": "root",
                "enable_graph": "FALSE"
            }
        },
        {
            "name": "只有空格的参数",
            "params": {
                "query": "小红",
                "user_id": "root",
                "score_range": "   ",
                "graph_entities": "  "
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for case in edge_cases:
            print(f"\n测试: {case['name']}")
            
            # 处理参数
            payload = {
                "query": case['params'].get("query", ""),
                "user_id": case['params'].get("user_id", ""),
                "limit": 10
            }
            
            # 应用修复逻辑
            enable_graph = case['params'].get("enable_graph")
            if enable_graph and enable_graph.strip() and enable_graph.lower() != "false":
                payload["enable_graph"] = enable_graph
            
            print(f"Payload: {json.dumps(payload, ensure_ascii=False)}")
            
            try:
                response = await client.post(f"{BASE_URL}/v2/memories/search/", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    memories = []
                    if "results" in data and isinstance(data["results"], dict):
                        if "results" in data["results"]:
                            memories = data["results"]["results"]
                    print(f"✅ 成功: 找到 {len(memories)} 个记忆")
                else:
                    print(f"❌ 失败: {response.status_code}")
            except Exception as e:
                print(f"❌ 异常: {e}")

async def main():
    """主函数"""
    
    print("🚀 开始最终验证")
    print("=" * 60)
    
    success = await final_verification()
    await test_edge_cases()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 最终验证成功！")
        print("✅ 修复已生效，Dify搜索工具现在可以正常工作")
        print("✅ 用户报告的问题已完全解决")
    else:
        print("💥 验证失败，需要进一步检查")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        print(f"\n{'✅ 验证通过' if result else '❌ 验证失败'}")
    except Exception as e:
        print(f"\n💥 验证异常: {e}")
