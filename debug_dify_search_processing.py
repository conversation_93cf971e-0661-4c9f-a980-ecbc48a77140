#!/usr/bin/env python3
"""
调试Dify搜索工具的完整处理流程
"""

import asyncio
import json
import httpx

BASE_URL = "http://localhost:8000"

class MockDifySearchTool:
    """模拟Dify搜索工具的完整处理逻辑"""
    
    async def search_memories_v2(self, tool_parameters):
        """完全模拟Dify工具的搜索逻辑"""
        
        # 提取参数（完全按照Dify工具的逻辑）
        query = tool_parameters.get("query", "")
        user_id = tool_parameters.get("user_id", "")
        
        # 验证必需参数
        if not query:
            return {"error": "Query is required."}
        if not user_id:
            return {"error": "User ID is required."}
        
        # 准备V2 payload（完全按照Dify工具的逻辑）
        payload = {
            "query": query
        }
        
        # 添加身份标识符
        if user_id:
            payload["user_id"] = user_id
        if tool_parameters.get("agent_id"):
            payload["agent_id"] = tool_parameters["agent_id"]
        if tool_parameters.get("run_id"):
            payload["run_id"] = tool_parameters["run_id"]
        
        # 初始化filters字典
        filters_dict = {}
        
        # 处理分页
        limit = tool_parameters.get("limit", 10)
        if limit and 1 <= limit <= 100:
            payload["limit"] = limit
        
        # 处理相似度阈值
        similarity_threshold = tool_parameters.get("similarity_threshold")
        if similarity_threshold is not None:
            filters_dict["similarity_threshold"] = similarity_threshold
        
        # 处理高级过滤器
        if tool_parameters.get("filters"):
            try:
                additional_filters = json.loads(tool_parameters["filters"])
                filters_dict.update(additional_filters)
            except json.JSONDecodeError as e:
                return {"error": f"Invalid JSON in filters: {str(e)}"}
        
        # 处理其他过滤条件
        if tool_parameters.get("score_range"):
            filters_dict["score_range"] = tool_parameters["score_range"]
        if tool_parameters.get("date_range"):
            filters_dict["date_range"] = tool_parameters["date_range"]
        if tool_parameters.get("category_filter"):
            filters_dict["categories"] = tool_parameters["category_filter"]
        
        # 将filters添加到payload
        if filters_dict:
            payload["filters"] = filters_dict
        
        # 图谱支持
        if tool_parameters.get("enable_graph"):
            payload["enable_graph"] = tool_parameters["enable_graph"]
        if tool_parameters.get("graph_entities"):
            payload["graph_entities"] = tool_parameters["graph_entities"]
        if tool_parameters.get("relationship_filter"):
            payload["relationship_filter"] = tool_parameters["relationship_filter"]
        
        print(f"📤 发送的payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        # 发送HTTP请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    f"{BASE_URL}/v2/memories/search/",
                    json=payload,
                    headers={"Authorization": "Token dummy-key"}
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                print(f"📥 收到的响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 处理响应（完全按照修复后的Dify逻辑）
                memories = []
                pagination_info = {}
                
                if isinstance(response_data, dict):
                    # 处理V2 API的实际响应格式
                    if "results" in response_data and isinstance(response_data["results"], dict):
                        if "results" in response_data["results"]:
                            memories = response_data["results"]["results"]
                            print(f"✅ 从results.results提取到 {len(memories)} 个记忆")
                    
                    # 构建分页信息
                    if "total_count" in response_data:
                        pagination_info["total_count"] = response_data["total_count"]
                    if "limit" in response_data:
                        pagination_info["limit"] = response_data["limit"]
                    
                    # 兼容旧格式
                    elif "memories" in response_data:
                        memories = response_data["memories"]
                        print(f"✅ 从memories字段提取到 {len(memories)} 个记忆")
                        if "pagination" in response_data:
                            pagination_info = response_data["pagination"]
                
                # 处理记忆数据
                processed_memories = []
                for memory in memories:
                    if not isinstance(memory, dict):
                        continue
                    
                    score_value = memory.get("score")
                    if score_value is None:
                        score_value = 0.0
                    
                    memory_item = {
                        "id": memory.get("id", "unknown"),
                        "memory": memory.get("memory", ""),
                        "score": score_value,
                        "categories": memory.get("categories", []),
                        "created_at": memory.get("created_at", ""),
                        "updated_at": memory.get("updated_at", ""),
                        "metadata": memory.get("metadata", {}),
                        "user_id": memory.get("user_id", ""),
                        "agent_id": memory.get("agent_id", ""),
                        "run_id": memory.get("run_id", "")
                    }
                    processed_memories.append(memory_item)
                
                print(f"🔄 处理后的记忆数量: {len(processed_memories)}")
                
                # 返回结果
                result = {
                    "query": query,
                    "api_version": "v2",
                    "memories": processed_memories,
                    "pagination": pagination_info,
                    "total_found": len(processed_memories)
                }
                
                # 生成文本响应
                text_response = f"Query: {query} (V2 API)\n\n"
                
                if processed_memories:
                    text_response += f"Found {len(processed_memories)} memories:\n"
                    for idx, memory in enumerate(processed_memories, 1):
                        text_response += f"\n{idx}. Memory: {memory['memory']}"
                        score_value = memory.get('score')
                        if score_value is not None:
                            text_response += f"\n   Score: {score_value:.3f}"
                        else:
                            text_response += f"\n   Score: N/A"
                        if memory['categories']:
                            text_response += f"\n   Categories: {', '.join(memory['categories'])}"
                        if memory['metadata']:
                            text_response += f"\n   Metadata: {memory['metadata']}"
                        text_response += f"\n   Created: {memory['created_at']}"
                else:
                    text_response += "No memories found."
                
                if pagination_info:
                    text_response += f"\n\nPagination: {pagination_info}"
                
                result["text"] = text_response
                return result
                
            except httpx.HTTPStatusError as e:
                error_message = f"HTTP error: {e.response.status_code}"
                try:
                    error_data = e.response.json()
                    if "detail" in error_data:
                        error_message = f"Error: {error_data['detail']}"
                    elif "error" in error_data:
                        error_message = f"Error: {error_data['error']}"
                except:
                    pass
                
                return {"error": error_message}
                
            except Exception as e:
                return {"error": str(e)}

async def debug_dify_processing():
    """调试Dify处理流程"""
    
    print("🔍 调试Dify搜索工具的完整处理流程")
    print("=" * 60)
    
    # 模拟用户的输入参数
    user_input = {
        "query": "小红",
        "user_id": "root",
        "score_range": "",
        "date_range": "",
        "enable_graph": "False",
        "graph_entities": "",
        "relationship_filter": ""
    }
    
    print(f"📋 用户输入参数:")
    print(json.dumps(user_input, indent=2, ensure_ascii=False))
    
    # 创建模拟工具并执行搜索
    tool = MockDifySearchTool()
    result = await tool.search_memories_v2(user_input)
    
    print(f"\n📊 最终结果:")
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
    else:
        print(f"✅ 成功处理")
        print(f"- 查询: {result['query']}")
        print(f"- API版本: {result['api_version']}")
        print(f"- 找到记忆数量: {result['total_found']}")
        print(f"- 分页信息: {result['pagination']}")
        
        if result['memories']:
            print(f"- 记忆详情:")
            for i, memory in enumerate(result['memories'][:3], 1):
                print(f"  {i}. {memory['memory']} (分数: {memory['score']:.3f})")
        
        print(f"\n📄 文本输出:")
        print(result['text'])
        
        print(f"\n📋 JSON输出:")
        json_output = {
            "text": result['text'],
            "files": [],
            "json": [result]
        }
        print(json.dumps(json_output, indent=2, ensure_ascii=False))

async def main():
    try:
        await debug_dify_processing()
        print("\n✅ 调试完成")
    except Exception as e:
        print(f"\n💥 调试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
